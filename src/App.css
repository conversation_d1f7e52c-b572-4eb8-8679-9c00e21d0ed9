#root {
  max-width: 1280px;
  margin: 0 auto;
  padding: 2rem;
  text-align: center;
}

.logo {
  height: 6em;
  padding: 1.5em;
  will-change: filter;
  transition: filter 300ms;
}
.logo:hover {
  filter: drop-shadow(0 0 2em #646cffaa);
}
.logo.react:hover {
  filter: drop-shadow(0 0 2em #61dafbaa);
}

@keyframes logo-spin {
  from {
    transform: rotate(0deg);
  }
  to {
    transform: rotate(360deg);
  }
}

@media (prefers-reduced-motion: no-preference) {
  a:nth-of-type(2) .logo {
    animation: logo-spin infinite 20s linear;
  }
}

.card {
  padding: 2em;
}

.read-the-docs {
  color: #888;
}

/* CC Styles */

.main-credit-card {
  max-width: 400px;
  margin: auto;
}

[data-component-name="CardContent"].p-8 {
    padding: 1.5rem;
}

.card-number {
  text-align: left;
  font-size: 1.2rem!important;
}

.main-credit-card .mb-8,
.main-credit-card .mb-6 {
    margin-bottom: 1rem;
}
