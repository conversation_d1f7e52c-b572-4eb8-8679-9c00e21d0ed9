
import React from 'react';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { CreditCard, TrendingUp, DollarSign, Calendar, AlertTriangle, CheckCircle } from 'lucide-react';
import { useDateContext } from '../contexts/DateContext';
import { format } from 'date-fns';

const PaymentSummary = () => {
  const { paymentSummary, selectedDate, transactions } = useDateContext();
  const utilizationPercentage = (paymentSummary.currentBalance / paymentSummary.creditLimit) * 100;

  // Calculate spending insights
  const getSpendingInsights = () => {
    if (transactions.length === 0) return null;

    // Group spending by category
    const categorySpending = transactions.reduce((acc, transaction) => {
      const category = transaction.category;
      if (!acc[category]) {
        acc[category] = { total: 0, count: 0 };
      }
      acc[category].total += transaction.amount;
      acc[category].count += 1;
      return acc;
    }, {} as Record<string, { total: number; count: number }>);

    // Sort categories by spending amount
    const sortedCategories = Object.entries(categorySpending)
      .sort(([, a], [, b]) => b.total - a.total)
      .slice(0, 3); // Top 3 categories

    const totalSpent = transactions.reduce((sum, t) => sum + t.amount, 0);
    const averageTransaction = totalSpent / transactions.length;

    // Get category emojis
    const getCategoryEmoji = (category: string) => {
      const emojiMap: Record<string, string> = {
        'Dining': '🍽️',
        'Coffee': '☕',
        'Groceries': '🛒',
        'Gas': '⛽',
        'Retail': '🛍️',
        'Fast Food': '🍔',
        'Electronics': '📱',
        'Pharmacy': '💊',
        'Home Improvement': '🔨',
        'Wholesale': '🏪',
        'Outdoor': '🏕️',
        'Travel': '✈️',
        'Transportation': '🚗'
      };
      return emojiMap[category] || '💳';
    };

    // Generate insights
    const topCategory = sortedCategories[0];
    const insights = [];

    // Add top categories
    sortedCategories.forEach(([category, data]) => {
      insights.push(`${getCategoryEmoji(category)} **${category}:** $${data.total.toFixed(2)} total spend.`);
    });

    // Add average transaction
    insights.push(`💸 Average transaction size: $${averageTransaction.toFixed(2)} (based on ${transactions.length} transactions).`);

    // Add personalized insights
    if (topCategory && topCategory[1].total > totalSpent * 0.4) {
      insights.push(`🎯 You have a significant focus on ${topCategory[0].toLowerCase()} this month—consider exploring more affordable options to balance your budget!`);
    }

    if (sortedCategories.some(([category]) => ['Dining', 'Coffee', 'Fast Food'].includes(category))) {
      const foodSpending = sortedCategories
        .filter(([category]) => ['Dining', 'Coffee', 'Fast Food'].includes(category))
        .reduce((sum, [, data]) => sum + data.total, 0);

      if (foodSpending < totalSpent * 0.3) {
        insights.push(`🌟 Great job on keeping food expenses reasonable!`);
      }
    }

    if (averageTransaction > 100) {
      insights.push(`💡 Your average transaction is quite high—consider breaking down larger purchases if possible.`);
    } else {
      insights.push(`✨ You're maintaining good control over individual transaction amounts!`);
    }

    return insights;
  };

  const spendingInsights = getSpendingInsights();

  const getUtilizationColor = () => {
    if (utilizationPercentage <= 30) return 'green';
    if (utilizationPercentage <= 70) return 'yellow';
    return 'red';
  };

  const getUtilizationIcon = () => {
    if (utilizationPercentage <= 30) return CheckCircle;
    if (utilizationPercentage <= 70) return AlertTriangle;
    return AlertTriangle;
  };

  const UtilizationIcon = getUtilizationIcon();

  return (
    <div className="space-y-8">
      {/* Holographic Month Header */}
      <div className="relative group">
        <div className="absolute -inset-1 bg-gradient-to-r from-cyan-400 to-purple-500 rounded-2xl blur opacity-25 group-hover:opacity-75 transition duration-300 animate-gradient"></div>
        <div className="relative flex items-center justify-between p-6 bg-white/10 backdrop-blur-xl rounded-2xl border border-white/20">
          <div className="flex items-center gap-3">
            <div className="w-10 h-10 bg-gradient-to-r from-cyan-400 to-blue-500 rounded-xl flex items-center justify-center animate-pulse-glow">
              <Calendar className="h-5 w-5 text-white animate-bounce" />
            </div>
            <span className="text-lg font-bold text-white">
              {format(selectedDate, 'MMMM yyyy')}
            </span>
          </div>
          <div className={`px-4 py-2 rounded-xl font-semibold text-sm transform hover:scale-105 transition-transform duration-300 ${
            paymentSummary.paymentStatus === 'On Time'
              ? 'bg-gradient-to-r from-green-400 to-emerald-500 text-white animate-pulse-glow'
              : 'bg-gradient-to-r from-red-400 to-pink-500 text-white animate-pulse-glow'
          }`}>
            {paymentSummary.paymentStatus}
          </div>
        </div>
      </div>

      {/* Cyberpunk Metrics Grid */}
      <div className="grid grid-cols-2 lg:grid-cols-4 gap-6">
        <div className="group relative">
          <div className="absolute -inset-0.5 bg-gradient-to-r from-red-400 to-pink-500 rounded-2xl blur opacity-25 group-hover:opacity-75 transition duration-300 animate-gradient"></div>
          <div className="relative bg-white/10 backdrop-blur-xl p-6 rounded-2xl border border-white/20 transform hover:scale-105 transition-all duration-300">
            <div className="flex items-center justify-between mb-4">
              <div className="w-12 h-12 bg-gradient-to-r from-red-400 to-pink-500 rounded-xl flex items-center justify-center shadow-lg animate-pulse-glow">
                <CreditCard className="h-6 w-6 text-white" />
              </div>
            </div>
            <p className="text-xs font-medium text-white/70 uppercase tracking-wide mb-2">Current Balance</p>
            <p className="text-2xl font-bold text-white mb-1 animate-gradient bg-gradient-to-r from-red-400 to-pink-400 bg-clip-text text-transparent">
              ${paymentSummary.currentBalance.toFixed(2)}
            </p>
            <p className="text-xs text-white/60">of ${paymentSummary.creditLimit.toLocaleString()} limit</p>
          </div>
        </div>

        <div className="group relative">
          <div className="absolute -inset-0.5 bg-gradient-to-r from-green-400 to-emerald-500 rounded-2xl blur opacity-25 group-hover:opacity-75 transition duration-300 animate-gradient"></div>
          <div className="relative bg-white/10 backdrop-blur-xl p-6 rounded-2xl border border-white/20 transform hover:scale-105 transition-all duration-300">
            <div className="flex items-center justify-between mb-4">
              <div className="w-12 h-12 bg-gradient-to-r from-green-400 to-emerald-500 rounded-xl flex items-center justify-center shadow-lg animate-pulse-glow">
                <DollarSign className="h-6 w-6 text-white" />
              </div>
            </div>
            <p className="text-xs font-medium text-white/70 uppercase tracking-wide mb-2">Available Credit</p>
            <p className="text-2xl font-bold text-white mb-1 animate-gradient bg-gradient-to-r from-green-400 to-emerald-400 bg-clip-text text-transparent">
              ${paymentSummary.availableCredit.toFixed(2)}
            </p>
            <p className="text-xs text-white/60">{((paymentSummary.availableCredit / paymentSummary.creditLimit) * 100).toFixed(1)}% available</p>
          </div>
        </div>

        <div className="group relative">
          <div className="absolute -inset-0.5 bg-gradient-to-r from-blue-400 to-cyan-500 rounded-2xl blur opacity-25 group-hover:opacity-75 transition duration-300 animate-gradient"></div>
          <div className="relative bg-white/10 backdrop-blur-xl p-6 rounded-2xl border border-white/20 transform hover:scale-105 transition-all duration-300">
            <div className="flex items-center justify-between mb-4">
              <div className="w-12 h-12 bg-gradient-to-r from-blue-400 to-cyan-500 rounded-xl flex items-center justify-center shadow-lg animate-pulse-glow">
                <TrendingUp className="h-6 w-6 text-white" />
              </div>
            </div>
            <p className="text-xs font-medium text-white/70 uppercase tracking-wide mb-2">Monthly Spend</p>
            <p className="text-2xl font-bold text-white mb-1 animate-gradient bg-gradient-to-r from-blue-400 to-cyan-400 bg-clip-text text-transparent">
              ${paymentSummary.totalSpent.toFixed(2)}
            </p>
            <p className="text-xs text-white/60">this month</p>
          </div>
        </div>

        <div className="group relative">
          <div className="absolute -inset-0.5 bg-gradient-to-r from-purple-400 to-pink-500 rounded-2xl blur opacity-25 group-hover:opacity-75 transition duration-300 animate-gradient"></div>
          <div className="relative bg-white/10 backdrop-blur-xl p-6 rounded-2xl border border-white/20 transform hover:scale-105 transition-all duration-300">
            <div className="flex items-center justify-between mb-4">
              <div className="w-12 h-12 bg-gradient-to-r from-purple-400 to-pink-500 rounded-xl flex items-center justify-center shadow-lg animate-pulse-glow">
                <DollarSign className="h-6 w-6 text-white" />
              </div>
            </div>
            <p className="text-xs font-medium text-white/70 uppercase tracking-wide mb-2">Cashback Earned</p>
            <p className="text-2xl font-bold text-white mb-1 animate-gradient bg-gradient-to-r from-purple-400 to-pink-400 bg-clip-text text-transparent">
              ${paymentSummary.cashbackEarned.toFixed(2)}
            </p>
            <p className="text-xs text-white/60">rewards earned</p>
          </div>
        </div>
      </div>

      {/* Credit Utilization */}
      <div className="bg-white p-6 rounded-lg border border-gray-200">
        <div className="flex justify-between items-center mb-4">
          <div className="flex items-center gap-2">
            <UtilizationIcon className={`h-5 w-5 ${
              getUtilizationColor() === 'green' ? 'text-green-600' :
              getUtilizationColor() === 'yellow' ? 'text-amber-600' :
              'text-red-600'
            }`} />
            <h3 className="text-sm font-semibold text-gray-900">Credit Utilization</h3>
          </div>
          <span className={`text-lg font-bold ${
            getUtilizationColor() === 'green' ? 'text-green-600' :
            getUtilizationColor() === 'yellow' ? 'text-amber-600' :
            'text-red-600'
          }`}>{utilizationPercentage.toFixed(1)}%</span>
        </div>
        <div className="w-full bg-gray-200 rounded-full h-2">
          <div
            className={`${
              getUtilizationColor() === 'green' ? 'bg-green-500' :
              getUtilizationColor() === 'yellow' ? 'bg-amber-500' :
              'bg-red-500'
            } h-2 rounded-full transition-all duration-500`}
            style={{ width: `${Math.min(utilizationPercentage, 100)}%` }}
          ></div>
        </div>
        <p className="text-xs text-gray-500 mt-2">
          {utilizationPercentage <= 30 ? 'Excellent utilization rate' :
           utilizationPercentage <= 70 ? 'Consider paying down balance' :
           'High utilization - pay down immediately'}
        </p>
      </div>

      {/* Payment Details */}
      <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
        <div className="bg-white p-4 rounded-lg border border-gray-200">
          <div className="flex items-center justify-between">
            <div>
              <p className="text-xs font-medium text-gray-500 uppercase tracking-wide">Minimum Payment</p>
              <p className="text-xl font-bold text-gray-900 mt-1">${paymentSummary.minimumPayment.toFixed(2)}</p>
            </div>
            <div className="w-8 h-8 bg-blue-50 rounded-lg flex items-center justify-center">
              <DollarSign className="h-4 w-4 text-blue-600" />
            </div>
          </div>
          <p className="text-xs text-gray-500 mt-2">due monthly</p>
        </div>

        <div className="bg-white p-4 rounded-lg border border-gray-200">
          <div className="flex items-center justify-between">
            <div>
              <p className="text-xs font-medium text-gray-500 uppercase tracking-wide">Due Date</p>
              <p className="text-lg font-bold text-gray-900 mt-1">{paymentSummary.dueDate}</p>
            </div>
            <div className="w-8 h-8 bg-orange-50 rounded-lg flex items-center justify-center">
              <Calendar className="h-4 w-4 text-orange-600" />
            </div>
          </div>
          <p className="text-xs text-gray-500 mt-2">next payment</p>
        </div>
      </div>

      {/* Payment Insights Section */}
      {spendingInsights && spendingInsights.length > 0 && (
        <div className="bg-white p-6 rounded-lg border border-gray-200">
          <div className="flex items-center gap-3 mb-4">
            <div className="w-8 h-8 bg-blue-50 rounded-lg flex items-center justify-center">
              <TrendingUp className="h-4 w-4 text-blue-600" />
            </div>
            <div>
              <h3 className="text-sm font-semibold text-gray-900">📊 Spending Insights</h3>
              <p className="text-xs text-gray-500">AI-powered analysis for {format(selectedDate, 'MMMM yyyy')}</p>
            </div>
          </div>

          <div className="bg-gray-50 rounded-lg p-4 border border-gray-200">
            <div className="space-y-3">
              {spendingInsights.map((insight, index) => (
                <div key={index} className="flex items-start gap-3">
                  <div className="w-1.5 h-1.5 bg-blue-500 rounded-full mt-2 flex-shrink-0"></div>
                  <p className="text-sm text-gray-700 leading-relaxed">
                    {insight.split('**').map((part, i) =>
                      i % 2 === 1 ? (
                        <span key={i} className="font-semibold text-gray-900">{part}</span>
                      ) : (
                        <span key={i}>{part}</span>
                      )
                    )}
                  </p>
                </div>
              ))}
            </div>
          </div>
        </div>
      )}
    </div>
  );
};

export default PaymentSummary;
