
import React from 'react';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { CreditCard, TrendingUp, DollarSign, Calendar, AlertTriangle, CheckCircle } from 'lucide-react';
import { useDateContext } from '../contexts/DateContext';
import { format } from 'date-fns';

const PaymentSummary = () => {
  const { paymentSummary, selectedDate, transactions } = useDateContext();
  const utilizationPercentage = (paymentSummary.currentBalance / paymentSummary.creditLimit) * 100;

  // Calculate spending insights
  const getSpendingInsights = () => {
    if (transactions.length === 0) return null;

    // Group spending by category
    const categorySpending = transactions.reduce((acc, transaction) => {
      const category = transaction.category;
      if (!acc[category]) {
        acc[category] = { total: 0, count: 0 };
      }
      acc[category].total += transaction.amount;
      acc[category].count += 1;
      return acc;
    }, {} as Record<string, { total: number; count: number }>);

    // Sort categories by spending amount
    const sortedCategories = Object.entries(categorySpending)
      .sort(([, a], [, b]) => b.total - a.total)
      .slice(0, 3); // Top 3 categories

    const totalSpent = transactions.reduce((sum, t) => sum + t.amount, 0);
    const averageTransaction = totalSpent / transactions.length;

    // Get category emojis
    const getCategoryEmoji = (category: string) => {
      const emojiMap: Record<string, string> = {
        'Dining': '🍽️',
        'Coffee': '☕',
        'Groceries': '🛒',
        'Gas': '⛽',
        'Retail': '🛍️',
        'Fast Food': '🍔',
        'Electronics': '📱',
        'Pharmacy': '💊',
        'Home Improvement': '🔨',
        'Wholesale': '🏪',
        'Outdoor': '🏕️',
        'Travel': '✈️',
        'Transportation': '🚗'
      };
      return emojiMap[category] || '💳';
    };

    // Generate insights
    const topCategory = sortedCategories[0];
    const insights = [];

    // Add top categories
    sortedCategories.forEach(([category, data]) => {
      insights.push(`${getCategoryEmoji(category)} **${category}:** $${data.total.toFixed(2)} total spend.`);
    });

    // Add average transaction
    insights.push(`💸 Average transaction size: $${averageTransaction.toFixed(2)} (based on ${transactions.length} transactions).`);

    // Add personalized insights
    if (topCategory && topCategory[1].total > totalSpent * 0.4) {
      insights.push(`🎯 You have a significant focus on ${topCategory[0].toLowerCase()} this month—consider exploring more affordable options to balance your budget!`);
    }

    if (sortedCategories.some(([category]) => ['Dining', 'Coffee', 'Fast Food'].includes(category))) {
      const foodSpending = sortedCategories
        .filter(([category]) => ['Dining', 'Coffee', 'Fast Food'].includes(category))
        .reduce((sum, [, data]) => sum + data.total, 0);

      if (foodSpending < totalSpent * 0.3) {
        insights.push(`🌟 Great job on keeping food expenses reasonable!`);
      }
    }

    if (averageTransaction > 100) {
      insights.push(`💡 Your average transaction is quite high—consider breaking down larger purchases if possible.`);
    } else {
      insights.push(`✨ You're maintaining good control over individual transaction amounts!`);
    }

    return insights;
  };

  const spendingInsights = getSpendingInsights();

  const getUtilizationColor = () => {
    if (utilizationPercentage <= 30) return 'green';
    if (utilizationPercentage <= 70) return 'yellow';
    return 'red';
  };

  const getUtilizationIcon = () => {
    if (utilizationPercentage <= 30) return CheckCircle;
    if (utilizationPercentage <= 70) return AlertTriangle;
    return AlertTriangle;
  };

  const UtilizationIcon = getUtilizationIcon();

  return (
    <div className="space-y-6">
      {/* Month Header */}
      <div className="flex items-center justify-between p-4 bg-gradient-to-r from-blue-50 to-indigo-50 rounded-xl border border-blue-200">
        <div className="flex items-center gap-3">
          <Calendar className="h-5 w-5 text-blue-600" />
          <span className="font-semibold text-blue-800">
            Viewing: {format(selectedDate, 'MMMM yyyy')}
          </span>
        </div>
        <Badge className={`${paymentSummary.paymentStatus === 'On Time' ? 'bg-green-500' : 'bg-orange-500'} text-white`}>
          {paymentSummary.paymentStatus}
        </Badge>
      </div>

      {/* Balance Overview */}
      <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
        <div className="p-6 bg-gradient-to-br from-red-50 to-pink-50 rounded-2xl border border-red-200 shadow-sm">
          <div className="flex items-center justify-between mb-3">
            <p className="text-sm text-red-600 font-semibold uppercase tracking-wide">Current Balance</p>
            <CreditCard className="h-5 w-5 text-red-500" />
          </div>
          <p className="text-3xl font-bold text-red-700">${paymentSummary.currentBalance.toFixed(2)}</p>
          <p className="text-xs text-red-600 mt-1">of ${paymentSummary.creditLimit.toLocaleString()} limit</p>
        </div>

        <div className="p-6 bg-gradient-to-br from-green-50 to-emerald-50 rounded-2xl border border-green-200 shadow-sm">
          <div className="flex items-center justify-between mb-3">
            <p className="text-sm text-green-600 font-semibold uppercase tracking-wide">Available Credit</p>
            <DollarSign className="h-5 w-5 text-green-500" />
          </div>
          <p className="text-3xl font-bold text-green-700">${paymentSummary.availableCredit.toFixed(2)}</p>
          <p className="text-xs text-green-600 mt-1">{((paymentSummary.availableCredit / paymentSummary.creditLimit) * 100).toFixed(1)}% available</p>
        </div>
      </div>

      {/* Credit Utilization */}
      <div className={`p-6 bg-gradient-to-r ${
        getUtilizationColor() === 'green' ? 'from-green-50 to-emerald-50 border-green-200' :
        getUtilizationColor() === 'yellow' ? 'from-yellow-50 to-amber-50 border-yellow-200' :
        'from-red-50 to-pink-50 border-red-200'
      } rounded-2xl border shadow-sm`}>
        <div className="flex justify-between items-center mb-4">
          <div className="flex items-center gap-2">
            <UtilizationIcon className={`h-5 w-5 ${
              getUtilizationColor() === 'green' ? 'text-green-600' :
              getUtilizationColor() === 'yellow' ? 'text-yellow-600' :
              'text-red-600'
            }`} />
            <p className={`text-sm font-semibold uppercase tracking-wide ${
              getUtilizationColor() === 'green' ? 'text-green-700' :
              getUtilizationColor() === 'yellow' ? 'text-yellow-700' :
              'text-red-700'
            }`}>Credit Utilization</p>
          </div>
          <p className={`text-lg font-bold ${
            getUtilizationColor() === 'green' ? 'text-green-800' :
            getUtilizationColor() === 'yellow' ? 'text-yellow-800' :
            'text-red-800'
          }`}>{utilizationPercentage.toFixed(1)}%</p>
        </div>
        <div className={`w-full ${
          getUtilizationColor() === 'green' ? 'bg-green-100' :
          getUtilizationColor() === 'yellow' ? 'bg-yellow-100' :
          'bg-red-100'
        } rounded-full h-4 border`}>
          <div
            className={`${
              getUtilizationColor() === 'green' ? 'bg-gradient-to-r from-green-400 to-emerald-500' :
              getUtilizationColor() === 'yellow' ? 'bg-gradient-to-r from-yellow-400 to-amber-500' :
              'bg-gradient-to-r from-red-400 to-pink-500'
            } h-4 rounded-full transition-all duration-700 shadow-sm`}
            style={{ width: `${Math.min(utilizationPercentage, 100)}%` }}
          ></div>
        </div>
        <p className={`text-xs mt-2 ${
          getUtilizationColor() === 'green' ? 'text-green-600' :
          getUtilizationColor() === 'yellow' ? 'text-yellow-600' :
          'text-red-600'
        }`}>
          {utilizationPercentage <= 30 ? 'Excellent utilization rate' :
           utilizationPercentage <= 70 ? 'Consider paying down balance' :
           'High utilization - pay down immediately'}
        </p>
      </div>

      {/* Payment Details */}
      <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
        <div className="p-6 bg-gradient-to-br from-cyan-50 to-blue-50 rounded-2xl border border-cyan-200 shadow-sm">
          <div className="flex items-center gap-3 mb-3">
            <div className="p-2 bg-cyan-100 rounded-lg">
              <DollarSign className="h-5 w-5 text-cyan-600" />
            </div>
            <div>
              <p className="text-sm text-cyan-600 font-semibold uppercase tracking-wide">Minimum Payment</p>
              <p className="text-2xl font-bold text-cyan-800">${paymentSummary.minimumPayment.toFixed(2)}</p>
            </div>
          </div>
        </div>

        <div className="p-6 bg-gradient-to-br from-violet-50 to-purple-50 rounded-2xl border border-violet-200 shadow-sm">
          <div className="flex items-center gap-3 mb-3">
            <div className="p-2 bg-violet-100 rounded-lg">
              <Calendar className="h-5 w-5 text-violet-600" />
            </div>
            <div>
              <p className="text-sm text-violet-600 font-semibold uppercase tracking-wide">Due Date</p>
              <p className="text-lg font-bold text-violet-800">{paymentSummary.dueDate}</p>
            </div>
          </div>
        </div>
      </div>

      {/* Monthly Summary */}
      <div className="p-6 bg-gradient-to-r from-emerald-50 to-green-50 rounded-2xl border border-emerald-200 shadow-sm">
        <div className="flex items-center justify-between">
          <div className="flex items-center gap-3">
            <div className="p-2 bg-emerald-100 rounded-lg">
              <TrendingUp className="h-6 w-6 text-emerald-600" />
            </div>
            <div>
              <p className="text-sm text-emerald-600 font-semibold uppercase tracking-wide">Monthly Performance</p>
              <div className="flex items-center gap-6 mt-1">
                <div>
                  <span className="text-lg font-bold text-emerald-800">
                    ${paymentSummary.totalSpent.toFixed(2)}
                  </span>
                  <span className="text-sm text-emerald-600 ml-1">spent</span>
                </div>
                <div>
                  <span className="text-lg font-bold text-emerald-800">
                    ${paymentSummary.cashbackEarned.toFixed(2)}
                  </span>
                  <span className="text-sm text-emerald-600 ml-1">cashback</span>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>

      {/* Payment Insights Section */}
      {spendingInsights && spendingInsights.length > 0 && (
        <div className="p-6 bg-gradient-to-r from-blue-50 to-indigo-50 rounded-2xl border border-blue-200 shadow-sm">
          <div className="flex items-center gap-3 mb-4">
            <div className="p-2 bg-blue-100 rounded-lg">
              <TrendingUp className="h-6 w-6 text-blue-600" />
            </div>
            <div>
              <h3 className="text-lg font-bold text-blue-800">📊 Your Current Month Spending Insights</h3>
              <p className="text-sm text-blue-600">Personalized analysis of your spending patterns</p>
            </div>
          </div>

          <div className="bg-white/60 rounded-xl p-4 border border-blue-200">
            <div className="space-y-3">
              {spendingInsights.map((insight, index) => (
                <div key={index} className="flex items-start gap-2">
                  <div className="w-1.5 h-1.5 bg-blue-500 rounded-full mt-2 flex-shrink-0"></div>
                  <p className="text-sm text-gray-700 leading-relaxed">
                    {insight.split('**').map((part, i) =>
                      i % 2 === 1 ? (
                        <span key={i} className="font-bold text-blue-800">{part}</span>
                      ) : (
                        <span key={i}>{part}</span>
                      )
                    )}
                  </p>
                </div>
              ))}
            </div>

            <div className="mt-4 pt-4 border-t border-blue-200">
              <p className="text-xs text-blue-600 italic">
                💡 Insights are generated based on your spending patterns for {format(selectedDate, 'MMMM yyyy')}
              </p>
            </div>
          </div>
        </div>
      )}
    </div>
  );
};

export default PaymentSummary;
