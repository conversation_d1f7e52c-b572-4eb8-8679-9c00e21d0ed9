
import React, { useState } from 'react';
import {
  CreditCard,
  TrendingUp,
  TrendingDown,
  DollarSign,
  Calendar,
  AlertTriangle,
  CheckCircle,
  ArrowUpRight,
  ArrowDownRight,
  MoreHorizontal,
  Download,
  Filter,
  RefreshCw
} from 'lucide-react';
import { useDateContext } from '../contexts/DateContext';
import { format } from 'date-fns';

interface PaymentSummaryProps {
  selectedDate?: Date;
}

const PaymentSummary: React.FC<PaymentSummaryProps> = ({ selectedDate }) => {
  const { paymentSummary, transactions } = useDateContext();
  const [activeTab, setActiveTab] = useState<'overview' | 'transactions' | 'analytics'>('overview');

  const utilizationPercentage = (paymentSummary.currentBalance / paymentSummary.creditLimit) * 100;

  // Enterprise-grade data calculations
  const monthlyData = {
    totalSpent: transactions.reduce((sum, t) => sum + t.amount, 0),
    transactionCount: transactions.length,
    averageTransaction: transactions.length > 0 ? transactions.reduce((sum, t) => sum + t.amount, 0) / transactions.length : 0,
    largestTransaction: transactions.length > 0 ? Math.max(...transactions.map(t => t.amount)) : 0,
    dailyAverage: transactions.length > 0 ? transactions.reduce((sum, t) => sum + t.amount, 0) / 30 : 0,
  };

  // Calculate month-over-month growth
  const previousMonthSpending = 2456.78; // This would come from API in real app
  const spendingGrowth = monthlyData.totalSpent > 0 ?
    ((monthlyData.totalSpent - previousMonthSpending) / previousMonthSpending) * 100 : 0;

  // Calculate spending insights
  const getSpendingInsights = () => {
    if (transactions.length === 0) return null;

    // Group spending by category
    const categorySpending = transactions.reduce((acc, transaction) => {
      const category = transaction.category;
      if (!acc[category]) {
        acc[category] = { total: 0, count: 0 };
      }
      acc[category].total += transaction.amount;
      acc[category].count += 1;
      return acc;
    }, {} as Record<string, { total: number; count: number }>);

    // Sort categories by spending amount
    const sortedCategories = Object.entries(categorySpending)
      .sort(([, a], [, b]) => b.total - a.total)
      .slice(0, 3); // Top 3 categories

    const totalSpent = transactions.reduce((sum, t) => sum + t.amount, 0);
    const averageTransaction = totalSpent / transactions.length;

    // Get category emojis
    const getCategoryEmoji = (category: string) => {
      const emojiMap: Record<string, string> = {
        'Dining': '🍽️',
        'Coffee': '☕',
        'Groceries': '🛒',
        'Gas': '⛽',
        'Retail': '🛍️',
        'Fast Food': '🍔',
        'Electronics': '📱',
        'Pharmacy': '💊',
        'Home Improvement': '🔨',
        'Wholesale': '🏪',
        'Outdoor': '🏕️',
        'Travel': '✈️',
        'Transportation': '🚗'
      };
      return emojiMap[category] || '💳';
    };

    // Generate insights
    const topCategory = sortedCategories[0];
    const insights = [];

    // Add top categories
    sortedCategories.forEach(([category, data]) => {
      insights.push(`${getCategoryEmoji(category)} **${category}:** $${data.total.toFixed(2)} total spend.`);
    });

    // Add average transaction
    insights.push(`💸 Average transaction size: $${averageTransaction.toFixed(2)} (based on ${transactions.length} transactions).`);

    // Add personalized insights
    if (topCategory && topCategory[1].total > totalSpent * 0.4) {
      insights.push(`🎯 You have a significant focus on ${topCategory[0].toLowerCase()} this month—consider exploring more affordable options to balance your budget!`);
    }

    if (sortedCategories.some(([category]) => ['Dining', 'Coffee', 'Fast Food'].includes(category))) {
      const foodSpending = sortedCategories
        .filter(([category]) => ['Dining', 'Coffee', 'Fast Food'].includes(category))
        .reduce((sum, [, data]) => sum + data.total, 0);

      if (foodSpending < totalSpent * 0.3) {
        insights.push(`🌟 Great job on keeping food expenses reasonable!`);
      }
    }

    if (averageTransaction > 100) {
      insights.push(`💡 Your average transaction is quite high—consider breaking down larger purchases if possible.`);
    } else {
      insights.push(`✨ You're maintaining good control over individual transaction amounts!`);
    }

    return insights;
  };

  const spendingInsights = getSpendingInsights();

  const getUtilizationColor = () => {
    if (utilizationPercentage <= 30) return 'green';
    if (utilizationPercentage <= 70) return 'yellow';
    return 'red';
  };

  const getUtilizationIcon = () => {
    if (utilizationPercentage <= 30) return CheckCircle;
    if (utilizationPercentage <= 70) return AlertTriangle;
    return AlertTriangle;
  };

  const UtilizationIcon = getUtilizationIcon();

  return (
    <div className="bg-white rounded-lg border border-gray-200 shadow-sm">
      {/* Enterprise Header */}
      <div className="px-6 py-4 border-b border-gray-200 bg-gray-50">
        <div className="flex items-center justify-between">
          <div className="flex items-center space-x-3">
            <div className="w-8 h-8 bg-blue-100 rounded-lg flex items-center justify-center">
              <TrendingUp className="h-4 w-4 text-blue-600" />
            </div>
            <div>
              <h3 className="text-lg font-semibold text-gray-900">Payment Overview</h3>
              <p className="text-sm text-gray-500">
                {selectedDate ? format(selectedDate, 'MMMM yyyy') : format(new Date(), 'MMMM yyyy')} • Financial Summary
              </p>
            </div>
          </div>
          <div className="flex items-center space-x-2">
            <button className="p-2 text-gray-400 hover:text-gray-600 hover:bg-gray-100 rounded-md">
              <RefreshCw className="h-4 w-4" />
            </button>
            <button className="p-2 text-gray-400 hover:text-gray-600 hover:bg-gray-100 rounded-md">
              <Download className="h-4 w-4" />
            </button>
            <button className="p-2 text-gray-400 hover:text-gray-600 hover:bg-gray-100 rounded-md">
              <MoreHorizontal className="h-4 w-4" />
            </button>
          </div>
        </div>
      </div>

      {/* Navigation Tabs */}
      <div className="px-6 py-3 border-b border-gray-200">
        <nav className="flex space-x-8">
          {[
            { id: 'overview', label: 'Overview', icon: TrendingUp },
            { id: 'transactions', label: 'Transactions', icon: CreditCard },
            { id: 'analytics', label: 'Analytics', icon: DollarSign }
          ].map((tab) => {
            const Icon = tab.icon;
            return (
              <button
                key={tab.id}
                onClick={() => setActiveTab(tab.id as any)}
                className={`flex items-center space-x-2 px-3 py-2 text-sm font-medium rounded-md transition-colors ${
                  activeTab === tab.id
                    ? 'text-blue-600 bg-blue-50 border-b-2 border-blue-600'
                    : 'text-gray-500 hover:text-gray-700 hover:bg-gray-50'
                }`}
              >
                <Icon className="h-4 w-4" />
                <span>{tab.label}</span>
              </button>
            );
          })}
        </nav>
      </div>

      {/* Content Area */}
      <div className="p-6">
        {activeTab === 'overview' && (
          <div className="space-y-6">
            {/* Key Performance Indicators */}
            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4">
              <div className="bg-gradient-to-r from-green-50 to-green-100 p-4 rounded-lg border border-green-200">
                <div className="flex items-center justify-between">
                  <div>
                    <p className="text-sm font-medium text-green-800">Available Credit</p>
                    <p className="text-2xl font-bold text-green-900">
                      ${paymentSummary.availableCredit.toLocaleString()}
                    </p>
                    <p className="text-xs text-green-600 mt-1">
                      {((paymentSummary.availableCredit / paymentSummary.creditLimit) * 100).toFixed(1)}% of limit
                    </p>
                  </div>
                  <div className="w-10 h-10 bg-green-200 rounded-full flex items-center justify-center">
                    <CheckCircle className="h-5 w-5 text-green-600" />
                  </div>
                </div>
              </div>

              <div className="bg-gradient-to-r from-blue-50 to-blue-100 p-4 rounded-lg border border-blue-200">
                <div className="flex items-center justify-between">
                  <div>
                    <p className="text-sm font-medium text-blue-800">Current Balance</p>
                    <p className="text-2xl font-bold text-blue-900">
                      ${paymentSummary.currentBalance.toLocaleString()}
                    </p>
                    <p className="text-xs text-blue-600 mt-1">
                      {utilizationPercentage.toFixed(1)}% utilization
                    </p>
                  </div>
                  <div className="w-10 h-10 bg-blue-200 rounded-full flex items-center justify-center">
                    <CreditCard className="h-5 w-5 text-blue-600" />
                  </div>
                </div>
              </div>

              <div className="bg-gradient-to-r from-purple-50 to-purple-100 p-4 rounded-lg border border-purple-200">
                <div className="flex items-center justify-between">
                  <div>
                    <p className="text-sm font-medium text-purple-800">Monthly Spending</p>
                    <p className="text-2xl font-bold text-purple-900">
                      ${monthlyData.totalSpent.toLocaleString()}
                    </p>
                    <div className="flex items-center mt-1">
                      {spendingGrowth >= 0 ? (
                        <ArrowUpRight className="h-3 w-3 text-red-500 mr-1" />
                      ) : (
                        <ArrowDownRight className="h-3 w-3 text-green-500 mr-1" />
                      )}
                      <p className={`text-xs ${spendingGrowth >= 0 ? 'text-red-600' : 'text-green-600'}`}>
                        {Math.abs(spendingGrowth).toFixed(1)}% vs last month
                      </p>
                    </div>
                  </div>
                  <div className="w-10 h-10 bg-purple-200 rounded-full flex items-center justify-center">
                    <DollarSign className="h-5 w-5 text-purple-600" />
                  </div>
                </div>
              </div>

              <div className="bg-gradient-to-r from-orange-50 to-orange-100 p-4 rounded-lg border border-orange-200">
                <div className="flex items-center justify-between">
                  <div>
                    <p className="text-sm font-medium text-orange-800">Next Payment</p>
                    <p className="text-2xl font-bold text-orange-900">
                      ${paymentSummary.minimumPayment.toLocaleString()}
                    </p>
                    <p className="text-xs text-orange-600 mt-1">
                      Due {paymentSummary.nextPaymentDate}
                    </p>
                  </div>
                  <div className="w-10 h-10 bg-orange-200 rounded-full flex items-center justify-center">
                    <Calendar className="h-5 w-5 text-orange-600" />
                  </div>
                </div>
              </div>
            </div>

            {/* Credit Utilization Analysis */}
            <div className="bg-white border border-gray-200 rounded-lg p-6">
              <div className="flex items-center justify-between mb-4">
                <h4 className="text-lg font-semibold text-gray-900">Credit Utilization Analysis</h4>
                <span className={`inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium ${
                  utilizationPercentage <= 30 ? 'bg-green-100 text-green-800' :
                  utilizationPercentage <= 70 ? 'bg-yellow-100 text-yellow-800' :
                  'bg-red-100 text-red-800'
                }`}>
                  {utilizationPercentage <= 30 ? 'Excellent' :
                   utilizationPercentage <= 70 ? 'Good' : 'Needs Attention'}
                </span>
              </div>

              <div className="space-y-4">
                <div>
                  <div className="flex justify-between text-sm mb-2">
                    <span className="text-gray-600">Current Utilization</span>
                    <span className="font-medium text-gray-900">{utilizationPercentage.toFixed(1)}%</span>
                  </div>
                  <div className="w-full bg-gray-200 rounded-full h-3">
                    <div
                      className={`h-3 rounded-full transition-all duration-500 ${
                        utilizationPercentage <= 30 ? 'bg-green-500' :
                        utilizationPercentage <= 70 ? 'bg-yellow-500' : 'bg-red-500'
                      }`}
                      style={{ width: `${Math.min(utilizationPercentage, 100)}%` }}
                    ></div>
                  </div>
                  <div className="flex justify-between text-xs text-gray-500 mt-1">
                    <span>0%</span>
                    <span>30% (Ideal)</span>
                    <span>70% (Good)</span>
                    <span>100%</span>
                  </div>
                </div>

                <div className="grid grid-cols-3 gap-4 pt-4 border-t border-gray-100">
                  <div className="text-center">
                    <p className="text-sm text-gray-500">Credit Limit</p>
                    <p className="text-lg font-semibold text-gray-900">
                      ${paymentSummary.creditLimit.toLocaleString()}
                    </p>
                  </div>
                  <div className="text-center">
                    <p className="text-sm text-gray-500">Used Credit</p>
                    <p className="text-lg font-semibold text-gray-900">
                      ${paymentSummary.currentBalance.toLocaleString()}
                    </p>
                  </div>
                  <div className="text-center">
                    <p className="text-sm text-gray-500">Available</p>
                    <p className="text-lg font-semibold text-green-600">
                      ${paymentSummary.availableCredit.toLocaleString()}
                    </p>
                  </div>
                </div>
              </div>
            </div>

            {/* Transaction Summary */}
            <div className="bg-white border border-gray-200 rounded-lg p-6">
              <h4 className="text-lg font-semibold text-gray-900 mb-4">Transaction Summary</h4>
              <div className="grid grid-cols-2 md:grid-cols-4 gap-4">
                <div className="text-center p-4 bg-gray-50 rounded-lg">
                  <p className="text-2xl font-bold text-gray-900">{monthlyData.transactionCount}</p>
                  <p className="text-sm text-gray-500">Total Transactions</p>
                </div>
                <div className="text-center p-4 bg-gray-50 rounded-lg">
                  <p className="text-2xl font-bold text-gray-900">${monthlyData.averageTransaction.toFixed(0)}</p>
                  <p className="text-sm text-gray-500">Average Amount</p>
                </div>
                <div className="text-center p-4 bg-gray-50 rounded-lg">
                  <p className="text-2xl font-bold text-gray-900">${monthlyData.largestTransaction.toFixed(0)}</p>
                  <p className="text-sm text-gray-500">Largest Transaction</p>
                </div>
                <div className="text-center p-4 bg-gray-50 rounded-lg">
                  <p className="text-2xl font-bold text-gray-900">${monthlyData.dailyAverage.toFixed(0)}</p>
                  <p className="text-sm text-gray-500">Daily Average</p>
                </div>
              </div>
            </div>

            {/* Payment Status */}
            <div className="bg-white border border-gray-200 rounded-lg p-6">
              <div className="flex items-center justify-between">
                <div>
                  <h4 className="text-lg font-semibold text-gray-900">Payment Status</h4>
                  <p className="text-sm text-gray-500 mt-1">Current account standing</p>
                </div>
                <div className="text-right">
                  <span className={`inline-flex items-center px-3 py-1 rounded-full text-sm font-medium ${
                    paymentSummary.paymentStatus === 'On Time'
                      ? 'bg-green-100 text-green-800'
                      : 'bg-red-100 text-red-800'
                  }`}>
                    {paymentSummary.paymentStatus === 'On Time' ? (
                      <CheckCircle className="h-4 w-4 mr-1" />
                    ) : (
                      <AlertTriangle className="h-4 w-4 mr-1" />
                    )}
                    {paymentSummary.paymentStatus}
                  </span>
                </div>
              </div>

              <div className="mt-4 grid grid-cols-1 md:grid-cols-3 gap-4">
                <div className="bg-blue-50 p-4 rounded-lg">
                  <p className="text-sm font-medium text-blue-800">Minimum Payment</p>
                  <p className="text-xl font-bold text-blue-900">${paymentSummary.minimumPayment}</p>
                  <p className="text-xs text-blue-600">Due {paymentSummary.nextPaymentDate}</p>
                </div>
                <div className="bg-green-50 p-4 rounded-lg">
                  <p className="text-sm font-medium text-green-800">Last Payment</p>
                  <p className="text-xl font-bold text-green-900">${paymentSummary.lastPaymentAmount}</p>
                  <p className="text-xs text-green-600">Paid {paymentSummary.lastPaymentDate}</p>
                </div>
                <div className="bg-purple-50 p-4 rounded-lg">
                  <p className="text-sm font-medium text-purple-800">Interest Rate</p>
                  <p className="text-xl font-bold text-purple-900">{paymentSummary.interestRate}%</p>
                  <p className="text-xs text-purple-600">Annual Percentage Rate</p>
                </div>
              </div>
            </div>
          </div>
        )}

        {activeTab === 'transactions' && (
          <div className="space-y-4">
            <div className="flex items-center justify-between">
              <h4 className="text-lg font-semibold text-gray-900">Recent Transactions</h4>
              <button className="flex items-center space-x-2 text-sm text-blue-600 hover:text-blue-700">
                <Filter className="h-4 w-4" />
                <span>Filter</span>
              </button>
            </div>

            <div className="bg-gray-50 rounded-lg p-4 text-center">
              <p className="text-gray-500">Transaction details would be displayed here</p>
              <p className="text-sm text-gray-400 mt-1">Integration with transaction data pending</p>
            </div>
          </div>
        )}

        {activeTab === 'analytics' && (
          <div className="space-y-4">
            <div className="flex items-center justify-between">
              <h4 className="text-lg font-semibold text-gray-900">Spending Analytics</h4>
              <button className="flex items-center space-x-2 text-sm text-blue-600 hover:text-blue-700">
                <Download className="h-4 w-4" />
                <span>Export</span>
              </button>
            </div>

            <div className="bg-gray-50 rounded-lg p-4 text-center">
              <p className="text-gray-500">Advanced analytics and charts would be displayed here</p>
              <p className="text-sm text-gray-400 mt-1">Detailed spending patterns and insights</p>
            </div>
          </div>
        )}
      </div>
    </div>
  );
};

export default PaymentSummary;
