
import React from 'react';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { CreditCard, TrendingUp, DollarSign, Calendar } from 'lucide-react';

const PaymentSummary = () => {
  const summaryData = {
    currentBalance: 2847.50,
    creditLimit: 10000,
    availableCredit: 7152.50,
    minimumPayment: 87.50,
    dueDate: 'March 15, 2024',
    totalSpent: 3420.75,
    cashbackEarned: 68.42,
    paymentStatus: 'On Time'
  };

  const utilizationPercentage = (summaryData.currentBalance / summaryData.creditLimit) * 100;

  return (
    <Card className="w-full bg-gradient-to-br from-blue-50 to-purple-50 border-blue-200">
      <CardHeader className="bg-gradient-to-r from-blue-600 to-purple-600 text-white rounded-t-lg">
        <CardTitle className="flex items-center gap-2">
          <CreditCard className="h-6 w-6" />
          Payment Summary
        </CardTitle>
        <CardDescription className="text-blue-100">Your current credit card overview</CardDescription>
      </CardHeader>
      <CardContent className="space-y-6 p-6">
        <div className="grid grid-cols-2 gap-4">
          <div className="space-y-2 p-4 bg-red-50 rounded-lg border border-red-200">
            <p className="text-sm text-red-600 font-medium">Current Balance</p>
            <p className="text-2xl font-bold text-red-700">${summaryData.currentBalance.toFixed(2)}</p>
          </div>
          <div className="space-y-2 p-4 bg-green-50 rounded-lg border border-green-200">
            <p className="text-sm text-green-600 font-medium">Available Credit</p>
            <p className="text-2xl font-bold text-green-700">${summaryData.availableCredit.toFixed(2)}</p>
          </div>
        </div>

        <div className="space-y-2 p-4 bg-gradient-to-r from-orange-50 to-amber-50 rounded-lg border border-orange-200">
          <div className="flex justify-between items-center">
            <p className="text-sm text-orange-700 font-medium">Credit Utilization</p>
            <p className="text-sm font-bold text-orange-800">{utilizationPercentage.toFixed(1)}%</p>
          </div>
          <div className="w-full bg-orange-100 rounded-full h-3 border border-orange-200">
            <div 
              className="bg-gradient-to-r from-orange-400 to-red-500 h-3 rounded-full transition-all duration-500 shadow-sm" 
              style={{ width: `${utilizationPercentage}%` }}
            ></div>
          </div>
        </div>

        <div className="grid grid-cols-2 gap-4 pt-4 border-t border-gray-200">
          <div className="flex items-center gap-3 p-3 bg-gradient-to-r from-cyan-50 to-blue-50 rounded-lg border border-cyan-200">
            <DollarSign className="h-8 w-8 text-cyan-600 bg-cyan-100 p-1.5 rounded-full" />
            <div>
              <p className="text-xs text-cyan-600 font-medium">Minimum Payment</p>
              <p className="font-bold text-cyan-800">${summaryData.minimumPayment}</p>
            </div>
          </div>
          <div className="flex items-center gap-3 p-3 bg-gradient-to-r from-violet-50 to-purple-50 rounded-lg border border-violet-200">
            <Calendar className="h-8 w-8 text-violet-600 bg-violet-100 p-1.5 rounded-full" />
            <div>
              <p className="text-xs text-violet-600 font-medium">Due Date</p>
              <p className="font-bold text-violet-800">{summaryData.dueDate}</p>
            </div>
          </div>
        </div>

        <div className="flex justify-between items-center pt-4 border-t border-gray-200 p-4 bg-gradient-to-r from-emerald-50 to-green-50 rounded-lg border border-emerald-200">
          <div className="flex items-center gap-2">
            <TrendingUp className="h-5 w-5 text-emerald-600" />
            <span className="text-sm text-emerald-700 font-medium">Cashback Earned: ${summaryData.cashbackEarned}</span>
          </div>
          <Badge className="bg-gradient-to-r from-green-500 to-emerald-500 text-white hover:from-green-600 hover:to-emerald-600">
            {summaryData.paymentStatus}
          </Badge>
        </div>
      </CardContent>
    </Card>
  );
};

export default PaymentSummary;
