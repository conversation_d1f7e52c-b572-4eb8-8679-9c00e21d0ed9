
import React from 'react';
import { Trophy, Star, Target, Zap, Coffee, ShoppingBag, Car, Utensils, Calendar } from 'lucide-react';
import { useDateContext } from '../contexts/DateContext';
import { format } from 'date-fns';
import GitHubBadge from './GitHubBadge';

const BadgesSection = () => {
  const { badges, selectedDate } = useDateContext();

  // Using badges from context instead of hardcoded data

  const earnedBadges = badges.filter(badge => badge.earned);
  const inProgressBadges = badges.filter(badge => !badge.earned);

  return (
    <div className="space-y-6">
      {/* Month Header */}
      <div className="flex items-center justify-between p-4 bg-gradient-to-r from-blue-50 to-indigo-50 rounded-xl border border-blue-200">
        <div className="flex items-center gap-3">
          <Calendar className="h-5 w-5 text-blue-600" />
          <span className="font-semibold text-blue-800">
            Progress for {format(selectedDate, 'MMMM yyyy')}
          </span>
        </div>
      </div>

      {/* Badge Summary */}
      <div className="flex items-center justify-between p-6 bg-gradient-to-r from-yellow-50 to-orange-50 rounded-2xl border border-yellow-300 shadow-sm">
        <div className="flex items-center gap-3">
          <div className="p-2 bg-yellow-100 rounded-lg">
            <Star className="h-6 w-6 text-yellow-600" />
          </div>
          <div>
            <span className="text-lg font-bold text-yellow-800">Earned Badges: {earnedBadges.length}</span>
            <p className="text-sm text-yellow-700">Great achievements this month!</p>
          </div>
        </div>
        <div className="flex items-center gap-3">
          <div className="p-2 bg-blue-100 rounded-lg">
            <Target className="h-5 w-5 text-blue-600" />
          </div>
          <div className="text-right">
            <span className="text-lg font-bold text-blue-800">{badges.length - earnedBadges.length} In Progress</span>
            <p className="text-sm text-blue-700">Keep going!</p>
          </div>
        </div>
      </div>

      <div className="space-y-4">
        <h4 className="font-bold text-sm text-green-700 uppercase tracking-wider flex items-center gap-2">
          <Trophy className="h-4 w-4" />
          Earned Badges
        </h4>
        <div className="flex flex-wrap gap-3">
          {earnedBadges.map((badge) => (
            <GitHubBadge key={badge.id} {...badge} />
          ))}
        </div>
      </div>

      <div className="space-y-4">
        <h4 className="font-bold text-sm text-gray-600 uppercase tracking-wider flex items-center gap-2">
          <Target className="h-4 w-4" />
          In Progress
        </h4>
        <div className="flex flex-wrap gap-3">
          {inProgressBadges.map((badge) => (
            <GitHubBadge key={badge.id} {...badge} />
          ))}
        </div>
      </div>

      <div className="mt-6 p-4 bg-gradient-to-r from-blue-50 to-purple-50 rounded-lg border border-blue-200">
        <h4 className="font-semibold text-blue-800 mb-2">🎯 Badge System</h4>
        <p className="text-sm text-blue-700">
          Click on any badge to see detailed requirements, progress, and tips for earning it. 
          Badges unlock special rewards and cashback bonuses!
        </p>
      </div>
    </div>
  );
};

export default BadgesSection;
