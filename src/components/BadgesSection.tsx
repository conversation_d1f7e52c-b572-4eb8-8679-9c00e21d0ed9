
import React from 'react';
import { Trophy, Star, Target, Zap, Coffee, ShoppingBag, Car, Utensils, Calendar } from 'lucide-react';
import { useDateContext } from '../contexts/DateContext';
import { format } from 'date-fns';
import GitHubBadge from './GitHubBadge';

const BadgesSection = () => {
  const { badges, selectedDate } = useDateContext();

  // Using badges from context instead of hardcoded data

  const earnedBadges = badges.filter(badge => badge.earned);
  const inProgressBadges = badges.filter(badge => !badge.earned);

  return (
    <div className="space-y-6">
      {/* Neon Month Header */}
      <div className="relative group">
        <div className="absolute -inset-1 bg-gradient-to-r from-pink-400 to-rose-500 rounded-2xl blur opacity-25 group-hover:opacity-75 transition duration-300 animate-gradient"></div>
        <div className="relative flex items-center justify-between p-4 bg-white/10 backdrop-blur-xl rounded-2xl border border-white/20">
          <div className="flex items-center gap-3">
            <div className="w-8 h-8 bg-gradient-to-r from-pink-400 to-rose-500 rounded-lg flex items-center justify-center animate-pulse-glow">
              <Calendar className="h-4 w-4 text-white animate-bounce" />
            </div>
            <span className="text-sm font-bold text-white">
              {format(selectedDate, 'MMMM yyyy')}
            </span>
          </div>
        </div>
      </div>

      {/* Holographic Badge Summary */}
      <div className="grid grid-cols-2 gap-4">
        <div className="group relative">
          <div className="absolute -inset-0.5 bg-gradient-to-r from-green-400 to-emerald-500 rounded-2xl blur opacity-25 group-hover:opacity-75 transition duration-300 animate-gradient"></div>
          <div className="relative p-4 bg-white/10 backdrop-blur-xl rounded-2xl border border-white/20 transform hover:scale-105 transition-all duration-300">
            <div className="flex items-center gap-3">
              <div className="w-10 h-10 bg-gradient-to-r from-green-400 to-emerald-500 rounded-xl flex items-center justify-center shadow-lg animate-pulse-glow">
                <Star className="h-5 w-5 text-white" />
              </div>
              <div>
                <p className="text-2xl font-bold text-white animate-gradient bg-gradient-to-r from-green-400 to-emerald-400 bg-clip-text text-transparent">
                  {earnedBadges.length}
                </p>
                <p className="text-xs text-white/70 font-semibold">EARNED</p>
              </div>
            </div>
          </div>
        </div>

        <div className="group relative">
          <div className="absolute -inset-0.5 bg-gradient-to-r from-blue-400 to-cyan-500 rounded-2xl blur opacity-25 group-hover:opacity-75 transition duration-300 animate-gradient"></div>
          <div className="relative p-4 bg-white/10 backdrop-blur-xl rounded-2xl border border-white/20 transform hover:scale-105 transition-all duration-300">
            <div className="flex items-center gap-3">
              <div className="w-10 h-10 bg-gradient-to-r from-blue-400 to-cyan-500 rounded-xl flex items-center justify-center shadow-lg animate-pulse-glow">
                <Target className="h-5 w-5 text-white" />
              </div>
              <div>
                <p className="text-2xl font-bold text-white animate-gradient bg-gradient-to-r from-blue-400 to-cyan-400 bg-clip-text text-transparent">
                  {badges.length - earnedBadges.length}
                </p>
                <p className="text-xs text-white/70 font-semibold">ACTIVE</p>
              </div>
            </div>
          </div>
        </div>
      </div>

      {earnedBadges.length > 0 && (
        <div className="space-y-3">
          <h4 className="font-semibold text-sm text-gray-900 flex items-center gap-2">
            <Trophy className="h-4 w-4 text-green-600" />
            Earned Badges
          </h4>
          <div className="grid grid-cols-1 gap-2">
            {earnedBadges.map((badge) => (
              <GitHubBadge key={badge.id} {...badge} />
            ))}
          </div>
        </div>
      )}

      {inProgressBadges.length > 0 && (
        <div className="space-y-3">
          <h4 className="font-semibold text-sm text-gray-900 flex items-center gap-2">
            <Target className="h-4 w-4 text-blue-600" />
            In Progress
          </h4>
          <div className="grid grid-cols-1 gap-2">
            {inProgressBadges.map((badge) => (
              <GitHubBadge key={badge.id} {...badge} />
            ))}
          </div>
        </div>
      )}

      {/* Cyberpunk Info Panel */}
      <div className="relative group">
        <div className="absolute -inset-1 bg-gradient-to-r from-purple-400 to-pink-500 rounded-2xl blur opacity-25 group-hover:opacity-75 transition duration-300 animate-gradient"></div>
        <div className="relative p-5 bg-white/10 backdrop-blur-xl rounded-2xl border border-white/20">
          <div className="flex items-center gap-3 mb-3">
            <div className="w-8 h-8 bg-gradient-to-r from-purple-400 to-pink-500 rounded-lg flex items-center justify-center animate-pulse-glow">
              <Trophy className="h-4 w-4 text-white" />
            </div>
            <h4 className="font-bold text-white text-sm">ACHIEVEMENT MATRIX</h4>
          </div>
          <p className="text-xs text-white/80 leading-relaxed">
            Neural-powered badge system. Unlock quantum rewards and cashback multipliers!
          </p>
          <div className="flex items-center gap-2 mt-3 text-white/60 text-xs">
            <div className="w-2 h-2 bg-purple-400 rounded-full animate-pulse"></div>
            <span>Quantum processing active</span>
          </div>
        </div>
      </div>
    </div>
  );
};

export default BadgesSection;
