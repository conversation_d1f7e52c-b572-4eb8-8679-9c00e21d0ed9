
import React from 'react';
import { Trophy, Star, Target, Zap, Coffee, ShoppingBag, Car, Utensils, Calendar } from 'lucide-react';
import { useDateContext } from '../contexts/DateContext';
import { format } from 'date-fns';
import GitHubBadge from './GitHubBadge';

const BadgesSection = () => {
  const { badges, selectedDate } = useDateContext();

  // Using badges from context instead of hardcoded data

  const earnedBadges = badges.filter(badge => badge.earned);
  const inProgressBadges = badges.filter(badge => !badge.earned);

  return (
    <div className="space-y-4">
      {/* Month Header */}
      <div className="flex items-center justify-between p-3 bg-gray-50 rounded-lg border border-gray-200">
        <div className="flex items-center gap-2">
          <Calendar className="h-4 w-4 text-gray-500" />
          <span className="text-sm font-medium text-gray-700">
            {format(selectedDate, 'MMMM yyyy')}
          </span>
        </div>
      </div>

      {/* Badge Summary */}
      <div className="grid grid-cols-2 gap-3">
        <div className="p-3 bg-green-50 rounded-lg border border-green-200">
          <div className="flex items-center gap-2">
            <div className="w-6 h-6 bg-green-100 rounded flex items-center justify-center">
              <Star className="h-3 w-3 text-green-600" />
            </div>
            <div>
              <p className="text-lg font-bold text-green-800">{earnedBadges.length}</p>
              <p className="text-xs text-green-600">Earned</p>
            </div>
          </div>
        </div>
        <div className="p-3 bg-blue-50 rounded-lg border border-blue-200">
          <div className="flex items-center gap-2">
            <div className="w-6 h-6 bg-blue-100 rounded flex items-center justify-center">
              <Target className="h-3 w-3 text-blue-600" />
            </div>
            <div>
              <p className="text-lg font-bold text-blue-800">{badges.length - earnedBadges.length}</p>
              <p className="text-xs text-blue-600">In Progress</p>
            </div>
          </div>
        </div>
      </div>

      {earnedBadges.length > 0 && (
        <div className="space-y-3">
          <h4 className="font-semibold text-sm text-gray-900 flex items-center gap-2">
            <Trophy className="h-4 w-4 text-green-600" />
            Earned Badges
          </h4>
          <div className="grid grid-cols-1 gap-2">
            {earnedBadges.map((badge) => (
              <GitHubBadge key={badge.id} {...badge} />
            ))}
          </div>
        </div>
      )}

      {inProgressBadges.length > 0 && (
        <div className="space-y-3">
          <h4 className="font-semibold text-sm text-gray-900 flex items-center gap-2">
            <Target className="h-4 w-4 text-blue-600" />
            In Progress
          </h4>
          <div className="grid grid-cols-1 gap-2">
            {inProgressBadges.map((badge) => (
              <GitHubBadge key={badge.id} {...badge} />
            ))}
          </div>
        </div>
      )}

      <div className="p-3 bg-blue-50 rounded-lg border border-blue-200">
        <h4 className="font-semibold text-blue-800 text-sm mb-1">🎯 Badge System</h4>
        <p className="text-xs text-blue-700 leading-relaxed">
          Click badges for details. Earn rewards and cashback bonuses!
        </p>
      </div>
    </div>
  );
};

export default BadgesSection;
