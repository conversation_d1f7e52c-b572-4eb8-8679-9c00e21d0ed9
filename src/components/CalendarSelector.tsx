import React, { useState } from 'react';
import { Button } from '@/components/ui/button';
import { Popover, PopoverContent, PopoverTrigger } from '@/components/ui/popover';
import { Calendar, ChevronLeft, ChevronRight, CalendarDays } from 'lucide-react';
import { format, subMonths, addMonths, startOfMonth, isSameMonth } from 'date-fns';

interface CalendarSelectorProps {
  selectedDate: Date;
  onDateChange: (date: Date) => void;
  maxDate?: Date;
  minDate?: Date;
}

const CalendarSelector: React.FC<CalendarSelectorProps> = ({
  selectedDate,
  onDateChange,
  maxDate = new Date(),
  minDate = subMonths(new Date(), 24)
}) => {
  const [isOpen, setIsOpen] = useState(false);
  const [viewDate, setViewDate] = useState(selectedDate);

  const handlePreviousMonth = () => {
    const newDate = subMonths(viewDate, 1);
    if (newDate >= minDate) {
      setViewDate(newDate);
    }
  };

  const handleNextMonth = () => {
    const newDate = addMonths(viewDate, 1);
    if (newDate <= maxDate) {
      setViewDate(newDate);
    }
  };

  const handleMonthSelect = (date: Date) => {
    const monthStart = startOfMonth(date);
    onDateChange(monthStart);
    setIsOpen(false);
  };

  return (
    <div className="relative group">
      <div className="absolute -inset-1 bg-gradient-to-r from-cyan-400 via-purple-500 to-pink-500 rounded-3xl blur opacity-25 group-hover:opacity-75 transition duration-1000 group-hover:duration-200 animate-gradient"></div>
      
      <div className="relative bg-white/10 backdrop-blur-xl rounded-3xl shadow-2xl border border-white/20 overflow-hidden">
        <div className="absolute inset-0 bg-gradient-to-r from-transparent via-white/10 to-transparent animate-shimmer"></div>
        
        <div className="relative px-8 py-6">
          <div className="flex items-center justify-between">
            <div className="flex items-center gap-4">
              <div className="w-14 h-14 bg-gradient-to-r from-cyan-400 to-purple-500 rounded-2xl flex items-center justify-center shadow-lg transform group-hover:scale-110 transition-transform duration-300 animate-pulse-glow">
                <CalendarDays className="h-7 w-7 text-white animate-bounce" />
              </div>
              <div>
                <h3 className="text-lg font-bold text-white mb-1">Time Portal</h3>
                <p className="text-white/70 text-sm">Navigate through financial dimensions</p>
              </div>
            </div>
            
            <div className="flex items-center gap-4">
              <div className="text-right">
                <div className="text-xl font-bold bg-gradient-to-r from-cyan-400 to-purple-400 bg-clip-text text-transparent animate-gradient">
                  {format(selectedDate, 'MMMM yyyy')}
                </div>
                <div className="text-white/60 text-sm">Active Timeline</div>
              </div>
              <Popover open={isOpen} onOpenChange={setIsOpen}>
                <PopoverTrigger asChild>
                  <Button 
                    className="bg-gradient-to-r from-purple-500 to-pink-500 hover:from-purple-600 hover:to-pink-600 text-white border-0 shadow-lg transform hover:scale-105 transition-all duration-300 px-6 py-3 rounded-xl font-semibold"
                  >
                    <Calendar className="h-4 w-4 mr-2 animate-pulse" />
                    Jump Timeline
                  </Button>
                </PopoverTrigger>
                <PopoverContent className="w-80 p-0" align="end">
                  <div className="bg-white rounded-lg shadow-xl border border-gray-200">
                    <div className="flex items-center justify-between p-4 border-b border-gray-200 bg-gradient-to-r from-blue-500 to-indigo-600 text-white rounded-t-lg">
                      <Button
                        variant="ghost"
                        size="sm"
                        onClick={handlePreviousMonth}
                        disabled={subMonths(viewDate, 1) < minDate}
                        className="text-white hover:bg-white/20"
                      >
                        <ChevronLeft className="h-4 w-4" />
                      </Button>
                      <h4 className="font-semibold text-lg">
                        {format(viewDate, 'yyyy')}
                      </h4>
                      <Button
                        variant="ghost"
                        size="sm"
                        onClick={handleNextMonth}
                        disabled={addMonths(viewDate, 1) > maxDate}
                        className="text-white hover:bg-white/20"
                      >
                        <ChevronRight className="h-4 w-4" />
                      </Button>
                    </div>
                    
                    <div className="p-4">
                      <div className="grid grid-cols-3 gap-2">
                        {Array.from({ length: 12 }, (_, i) => {
                          const monthDate = new Date(viewDate.getFullYear(), i, 1);
                          const isSelected = isSameMonth(monthDate, selectedDate);
                          const isDisabled = monthDate < minDate || monthDate > maxDate;
                          
                          return (
                            <Button
                              key={i}
                              variant={isSelected ? "default" : "ghost"}
                              size="sm"
                              onClick={() => handleMonthSelect(monthDate)}
                              disabled={isDisabled}
                              className={`h-12 text-sm font-medium transition-all duration-200 ${
                                isSelected 
                                  ? 'bg-blue-600 text-white shadow-md' 
                                  : 'hover:bg-blue-50 text-gray-700'
                              } ${isDisabled ? 'opacity-40 cursor-not-allowed' : ''}`}
                            >
                              {format(monthDate, 'MMM')}
                            </Button>
                          );
                        })}
                      </div>
                    </div>
                    
                    <div className="p-4 border-t border-gray-200 bg-gray-50 rounded-b-lg">
                      <p className="text-xs text-gray-600 mb-2 font-medium">Quick Select:</p>
                      <div className="flex gap-2">
                        <Button
                          variant="outline"
                          size="sm"
                          onClick={() => handleMonthSelect(new Date())}
                          className="text-xs bg-white hover:bg-blue-50 border-blue-200"
                        >
                          This Month
                        </Button>
                        <Button
                          variant="outline"
                          size="sm"
                          onClick={() => handleMonthSelect(subMonths(new Date(), 1))}
                          className="text-xs bg-white hover:bg-blue-50 border-blue-200"
                        >
                          Last Month
                        </Button>
                      </div>
                    </div>
                  </div>
                </PopoverContent>
              </Popover>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
};

export default CalendarSelector;
