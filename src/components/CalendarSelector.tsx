import React, { useState } from 'react';
import { Card, CardContent } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Popover, PopoverContent, PopoverTrigger } from '@/components/ui/popover';
import { Calendar, ChevronLeft, ChevronRight, CalendarDays } from 'lucide-react';
import { format, subMonths, addMonths, startOfMonth, isSameMonth } from 'date-fns';

interface CalendarSelectorProps {
  selectedDate: Date;
  onDateChange: (date: Date) => void;
  maxDate?: Date;
  minDate?: Date;
}

const CalendarSelector: React.FC<CalendarSelectorProps> = ({
  selectedDate,
  onDateChange,
  maxDate = new Date(),
  minDate = subMonths(new Date(), 24) // Allow up to 24 months back
}) => {
  const [isOpen, setIsOpen] = useState(false);
  const [viewDate, setViewDate] = useState(selectedDate);

  const handlePreviousMonth = () => {
    const newDate = subMonths(viewDate, 1);
    if (newDate >= minDate) {
      setViewDate(newDate);
    }
  };

  const handleNextMonth = () => {
    const newDate = addMonths(viewDate, 1);
    if (newDate <= maxDate) {
      setViewDate(newDate);
    }
  };

  const handleMonthSelect = (date: Date) => {
    const monthStart = startOfMonth(date);
    onDateChange(monthStart);
    setIsOpen(false);
  };

  const generateMonthOptions = () => {
    const months = [];
    let currentMonth = startOfMonth(maxDate);
    
    while (currentMonth >= minDate) {
      months.push(currentMonth);
      currentMonth = subMonths(currentMonth, 1);
    }
    
    return months;
  };

  const monthOptions = generateMonthOptions();

  return (
    <Card className="bg-gradient-to-r from-blue-50 via-indigo-50 to-purple-50 border-blue-200 shadow-lg">
      <CardContent className="p-6">
        <div className="flex items-center justify-between">
          <div className="flex items-center gap-3">
            <div className="p-2 bg-blue-100 rounded-lg">
              <CalendarDays className="h-6 w-6 text-blue-600" />
            </div>
            <div>
              <h3 className="text-lg font-bold text-gray-800">Viewing Period</h3>
              <p className="text-sm text-gray-600">Select month to view transactions</p>
            </div>
          </div>
          
          <div className="flex items-center gap-2">
            <Popover open={isOpen} onOpenChange={setIsOpen}>
              <PopoverTrigger asChild>
                <Button 
                  variant="outline" 
                  className="bg-white/80 border-blue-300 hover:bg-blue-50 text-blue-800 font-semibold px-6 py-2 h-auto"
                >
                  <Calendar className="h-4 w-4 mr-2" />
                  {format(selectedDate, 'MMMM yyyy')}
                </Button>
              </PopoverTrigger>
              <PopoverContent className="w-80 p-0" align="end">
                <div className="bg-white rounded-lg shadow-xl border border-gray-200">
                  {/* Header */}
                  <div className="flex items-center justify-between p-4 border-b border-gray-200 bg-gradient-to-r from-blue-500 to-indigo-600 text-white rounded-t-lg">
                    <Button
                      variant="ghost"
                      size="sm"
                      onClick={handlePreviousMonth}
                      disabled={subMonths(viewDate, 1) < minDate}
                      className="text-white hover:bg-white/20"
                    >
                      <ChevronLeft className="h-4 w-4" />
                    </Button>
                    <h4 className="font-semibold text-lg">
                      {format(viewDate, 'yyyy')}
                    </h4>
                    <Button
                      variant="ghost"
                      size="sm"
                      onClick={handleNextMonth}
                      disabled={addMonths(viewDate, 1) > maxDate}
                      className="text-white hover:bg-white/20"
                    >
                      <ChevronRight className="h-4 w-4" />
                    </Button>
                  </div>
                  
                  {/* Month Grid */}
                  <div className="p-4">
                    <div className="grid grid-cols-3 gap-2">
                      {Array.from({ length: 12 }, (_, i) => {
                        const monthDate = new Date(viewDate.getFullYear(), i, 1);
                        const isSelected = isSameMonth(monthDate, selectedDate);
                        const isDisabled = monthDate < minDate || monthDate > maxDate;
                        
                        return (
                          <Button
                            key={i}
                            variant={isSelected ? "default" : "ghost"}
                            size="sm"
                            onClick={() => handleMonthSelect(monthDate)}
                            disabled={isDisabled}
                            className={`
                              h-12 text-sm font-medium transition-all duration-200
                              ${isSelected 
                                ? 'bg-blue-600 text-white shadow-md' 
                                : 'hover:bg-blue-50 text-gray-700'
                              }
                              ${isDisabled ? 'opacity-40 cursor-not-allowed' : ''}
                            `}
                          >
                            {format(monthDate, 'MMM')}
                          </Button>
                        );
                      })}
                    </div>
                  </div>
                  
                  {/* Quick Select */}
                  <div className="p-4 border-t border-gray-200 bg-gray-50 rounded-b-lg">
                    <p className="text-xs text-gray-600 mb-2 font-medium">Quick Select:</p>
                    <div className="flex gap-2">
                      <Button
                        variant="outline"
                        size="sm"
                        onClick={() => handleMonthSelect(new Date())}
                        className="text-xs bg-white hover:bg-blue-50 border-blue-200"
                      >
                        This Month
                      </Button>
                      <Button
                        variant="outline"
                        size="sm"
                        onClick={() => handleMonthSelect(subMonths(new Date(), 1))}
                        className="text-xs bg-white hover:bg-blue-50 border-blue-200"
                      >
                        Last Month
                      </Button>
                    </div>
                  </div>
                </div>
              </PopoverContent>
            </Popover>
          </div>
        </div>
        
        {/* Current Selection Info */}
        <div className="mt-4 p-3 bg-white/60 rounded-lg border border-blue-200">
          <div className="flex items-center justify-between text-sm">
            <span className="text-gray-600">Selected Period:</span>
            <span className="font-semibold text-blue-800">
              {format(selectedDate, 'MMMM yyyy')}
            </span>
          </div>
        </div>
      </CardContent>
    </Card>
  );
};

export default CalendarSelector;
