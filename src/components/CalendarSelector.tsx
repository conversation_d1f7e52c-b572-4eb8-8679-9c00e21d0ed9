import React, { useState } from 'react';
import { Button } from '@/components/ui/button';
import { Popover, PopoverContent, PopoverTrigger } from '@/components/ui/popover';
import { Calendar, ChevronLeft, ChevronRight, CalendarDays } from 'lucide-react';
import { format, subMonths, addMonths, startOfMonth, isSameMonth } from 'date-fns';

interface CalendarSelectorProps {
  selectedDate: Date;
  onDateChange: (date: Date) => void;
  maxDate?: Date;
  minDate?: Date;
}

const CalendarSelector: React.FC<CalendarSelectorProps> = ({
  selectedDate,
  onDateChange,
  maxDate = new Date(),
  minDate = subMonths(new Date(), 24) // Allow up to 24 months back
}) => {
  const [isOpen, setIsOpen] = useState(false);
  const [viewDate, setViewDate] = useState(selectedDate);

  const handlePreviousMonth = () => {
    const newDate = subMonths(viewDate, 1);
    if (newDate >= minDate) {
      setViewDate(newDate);
    }
  };

  const handleNextMonth = () => {
    const newDate = addMonths(viewDate, 1);
    if (newDate <= maxDate) {
      setViewDate(newDate);
    }
  };

  const handleMonthSelect = (date: Date) => {
    const monthStart = startOfMonth(date);
    onDateChange(monthStart);
    setIsOpen(false);
  };





  return (
    <div className="bg-white rounded-xl shadow-sm border border-gray-200">
      <div className="px-6 py-4">
        <div className="flex items-center justify-between">
          <div className="flex items-center gap-3">
            <div className="w-10 h-10 bg-blue-50 rounded-lg flex items-center justify-center">
              <CalendarDays className="h-5 w-5 text-blue-600" />
            </div>
            <div>
              <h3 className="text-sm font-semibold text-gray-900">Viewing Period</h3>
              <p className="text-xs text-gray-500">Select month to analyze</p>
            </div>
          </div>

          <div className="flex items-center gap-3">
            <div className="text-right">
              <div className="text-sm font-medium text-gray-900">{format(selectedDate, 'MMMM yyyy')}</div>
              <div className="text-xs text-gray-500">Current selection</div>
            </div>
            <Popover open={isOpen} onOpenChange={setIsOpen}>
              <PopoverTrigger asChild>
                <Button
                  variant="outline"
                  size="sm"
                  className="border-gray-300 hover:bg-gray-50 text-gray-700"
                >
                  <Calendar className="h-4 w-4 mr-2" />
                  Change
                </Button>
              </PopoverTrigger>
              <PopoverContent className="w-80 p-0" align="end">
                <div className="bg-white rounded-lg shadow-xl border border-gray-200">
                  {/* Header */}
                  <div className="flex items-center justify-between p-4 border-b border-gray-200 bg-gradient-to-r from-blue-500 to-indigo-600 text-white rounded-t-lg">
                    <Button
                      variant="ghost"
                      size="sm"
                      onClick={handlePreviousMonth}
                      disabled={subMonths(viewDate, 1) < minDate}
                      className="text-white hover:bg-white/20"
                    >
                      <ChevronLeft className="h-4 w-4" />
                    </Button>
                    <h4 className="font-semibold text-lg">
                      {format(viewDate, 'yyyy')}
                    </h4>
                    <Button
                      variant="ghost"
                      size="sm"
                      onClick={handleNextMonth}
                      disabled={addMonths(viewDate, 1) > maxDate}
                      className="text-white hover:bg-white/20"
                    >
                      <ChevronRight className="h-4 w-4" />
                    </Button>
                  </div>
                  
                  {/* Month Grid */}
                  <div className="p-4">
                    <div className="grid grid-cols-3 gap-2">
                      {Array.from({ length: 12 }, (_, i) => {
                        const monthDate = new Date(viewDate.getFullYear(), i, 1);
                        const isSelected = isSameMonth(monthDate, selectedDate);
                        const isDisabled = monthDate < minDate || monthDate > maxDate;
                        
                        return (
                          <Button
                            key={i}
                            variant={isSelected ? "default" : "ghost"}
                            size="sm"
                            onClick={() => handleMonthSelect(monthDate)}
                            disabled={isDisabled}
                            className={`
                              h-12 text-sm font-medium transition-all duration-200
                              ${isSelected 
                                ? 'bg-blue-600 text-white shadow-md' 
                                : 'hover:bg-blue-50 text-gray-700'
                              }
                              ${isDisabled ? 'opacity-40 cursor-not-allowed' : ''}
                            `}
                          >
                            {format(monthDate, 'MMM')}
                          </Button>
                        );
                      })}
                    </div>
                  </div>
                  
                  {/* Quick Select */}
                  <div className="p-4 border-t border-gray-200 bg-gray-50 rounded-b-lg">
                    <p className="text-xs text-gray-600 mb-2 font-medium">Quick Select:</p>
                    <div className="flex gap-2">
                      <Button
                        variant="outline"
                        size="sm"
                        onClick={() => handleMonthSelect(new Date())}
                        className="text-xs bg-white hover:bg-blue-50 border-blue-200"
                      >
                        This Month
                      </Button>
                      <Button
                        variant="outline"
                        size="sm"
                        onClick={() => handleMonthSelect(subMonths(new Date(), 1))}
                        className="text-xs bg-white hover:bg-blue-50 border-blue-200"
                      >
                        Last Month
                      </Button>
                    </div>
                  </div>
                </div>
              </PopoverContent>
            </Popover>
          </div>
        </div>
      </div>
    </div>
  );
};

export default CalendarSelector;
