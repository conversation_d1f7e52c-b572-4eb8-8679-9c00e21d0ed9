
import React from 'react';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { Lightbulb, TrendingUp, AlertTriangle, DollarSign, CreditCard, Target, Calendar } from 'lucide-react';
import { useDateContext } from '../contexts/DateContext';
import { format } from 'date-fns';

const SmartSuggestions = () => {
  const { suggestions, selectedDate } = useDateContext();

  // Using suggestions from context instead of hardcoded data

  const getPriorityColor = (priority: string) => {
    switch (priority) {
      case 'high': return 'bg-gradient-to-r from-red-500 to-pink-500';
      case 'medium': return 'bg-gradient-to-r from-yellow-500 to-orange-500';
      case 'low': return 'bg-gradient-to-r from-blue-500 to-cyan-500';
      default: return 'bg-gray-500';
    }
  };

  const getPriorityBgColor = (priority: string, color: string) => {
    const baseColors = {
      high: 'bg-gradient-to-r from-red-50 to-pink-50 border-red-300',
      medium: 'bg-gradient-to-r from-yellow-50 to-orange-50 border-yellow-300',
      low: 'bg-gradient-to-r from-blue-50 to-cyan-50 border-blue-300'
    };
    return baseColors[priority as keyof typeof baseColors] || 'bg-gray-50 border-gray-200';
  };

  const getIconBgColor = (color: string) => {
    const colorMap = {
      green: 'bg-gradient-to-r from-green-100 to-emerald-100 text-green-700',
      yellow: 'bg-gradient-to-r from-yellow-100 to-amber-100 text-yellow-700',
      blue: 'bg-gradient-to-r from-blue-100 to-cyan-100 text-blue-700',
      red: 'bg-gradient-to-r from-red-100 to-pink-100 text-red-700',
      purple: 'bg-gradient-to-r from-purple-100 to-violet-100 text-purple-700'
    };
    return colorMap[color as keyof typeof colorMap] || 'bg-gray-100 text-gray-600';
  };

  return (
    <div className="space-y-6">
      {/* Month Header */}
      <div className="flex items-center justify-between p-4 bg-gradient-to-r from-blue-50 to-indigo-50 rounded-xl border border-blue-200">
        <div className="flex items-center gap-3">
          <Calendar className="h-5 w-5 text-blue-600" />
          <span className="font-semibold text-blue-800">
            Suggestions for {format(selectedDate, 'MMMM yyyy')}
          </span>
        </div>
        <Badge className="bg-purple-100 text-purple-800 border-purple-200">
          {suggestions.length} insights
        </Badge>
      </div>

      {/* Suggestions Content */}
      <div className="space-y-4">
        {suggestions.map((suggestion) => {
          // Map suggestion types to icons
          const getIcon = (type: string) => {
            switch (type) {
              case 'cashback': return DollarSign;
              case 'spending': return CreditCard;
              case 'reward': return TrendingUp;
              case 'warning': return AlertTriangle;
              case 'goal': return Target;
              default: return Lightbulb;
            }
          };

          const IconComponent = getIcon(suggestion.type);
          return (
            <div
              key={suggestion.id}
              className={`p-6 rounded-2xl border-2 ${getPriorityBgColor(suggestion.priority, suggestion.color)} shadow-sm hover:shadow-lg transition-all duration-200`}
            >
              <div className="flex items-start gap-4">
                <div className={`p-3 rounded-full shadow-sm ${getIconBgColor(suggestion.color)}`}>
                  <IconComponent className="h-5 w-5" />
                </div>
                <div className="flex-1 space-y-3">
                  <div className="flex items-start justify-between">
                    <h4 className="font-bold text-gray-800">{suggestion.title}</h4>
                    <Badge className={`text-white text-xs font-bold ${getPriorityColor(suggestion.priority)}`}>
                      {suggestion.priority.toUpperCase()}
                    </Badge>
                  </div>
                  <p className="text-sm text-gray-700 leading-relaxed">{suggestion.description}</p>
                  <div className="flex items-center justify-between">
                    <Badge variant="outline" className="text-xs font-medium border-gray-300">
                      {suggestion.category}
                    </Badge>
                    {suggestion.potentialSavings && (
                      <span className="text-sm font-bold text-green-600 bg-green-50 px-2 py-1 rounded-full">
                        💰 Save ${suggestion.potentialSavings}
                      </span>
                    )}
                  </div>
                </div>
              </div>
            </div>
          );
        })}

        {/* Monthly Impact Summary */}
        <div className="mt-6 p-6 bg-gradient-to-r from-emerald-50 to-green-50 border-2 border-emerald-300 rounded-2xl shadow-sm">
          <div className="flex items-center gap-3 mb-4">
            <div className="p-2 bg-emerald-100 rounded-lg">
              <TrendingUp className="h-6 w-6 text-emerald-600" />
            </div>
            <div>
              <span className="font-bold text-xl text-emerald-800">Monthly Impact</span>
              <p className="text-sm text-emerald-600">Potential savings from following suggestions</p>
            </div>
          </div>
          <div className="space-y-3">
            <p className="text-gray-700 leading-relaxed">
              Following our suggestions could save you{' '}
              <span className="font-bold text-2xl text-emerald-700">
                ${suggestions.reduce((sum, s) => sum + (s.potentialSavings || 0), 0).toFixed(2)}
              </span>{' '}
              this month
            </p>
            <div className="text-sm text-gray-600 bg-white/60 p-4 rounded-xl border border-emerald-200">
              📈 Keep optimizing your spending patterns for maximum rewards!
            </div>
          </div>
        </div>
      </div>
    </div>
  );
};

export default SmartSuggestions;
