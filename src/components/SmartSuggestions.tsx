
import React from 'react';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { Lightbulb, TrendingUp, AlertTriangle, DollarSign, CreditCard, Target, Calendar } from 'lucide-react';
import { useDateContext } from '../contexts/DateContext';
import { format } from 'date-fns';

const SmartSuggestions = () => {
  const { suggestions, selectedDate } = useDateContext();

  // Using suggestions from context instead of hardcoded data

  const getPriorityColor = (priority: string) => {
    switch (priority) {
      case 'high': return 'bg-gradient-to-r from-red-500 to-pink-500';
      case 'medium': return 'bg-gradient-to-r from-yellow-500 to-orange-500';
      case 'low': return 'bg-gradient-to-r from-blue-500 to-cyan-500';
      default: return 'bg-gray-500';
    }
  };

  const getPriorityBgColor = (priority: string, color: string) => {
    const baseColors = {
      high: 'bg-gradient-to-r from-red-50 to-pink-50 border-red-300',
      medium: 'bg-gradient-to-r from-yellow-50 to-orange-50 border-yellow-300',
      low: 'bg-gradient-to-r from-blue-50 to-cyan-50 border-blue-300'
    };
    return baseColors[priority as keyof typeof baseColors] || 'bg-gray-50 border-gray-200';
  };

  const getIconBgColor = (color: string) => {
    const colorMap = {
      green: 'bg-gradient-to-r from-green-100 to-emerald-100 text-green-700',
      yellow: 'bg-gradient-to-r from-yellow-100 to-amber-100 text-yellow-700',
      blue: 'bg-gradient-to-r from-blue-100 to-cyan-100 text-blue-700',
      red: 'bg-gradient-to-r from-red-100 to-pink-100 text-red-700',
      purple: 'bg-gradient-to-r from-purple-100 to-violet-100 text-purple-700'
    };
    return colorMap[color as keyof typeof colorMap] || 'bg-gray-100 text-gray-600';
  };

  return (
    <div className="space-y-4">
      {/* Month Header */}
      <div className="flex items-center justify-between p-3 bg-gray-50 rounded-lg border border-gray-200">
        <div className="flex items-center gap-2">
          <Calendar className="h-4 w-4 text-gray-500" />
          <span className="text-sm font-medium text-gray-700">
            {format(selectedDate, 'MMMM yyyy')}
          </span>
        </div>
        <Badge variant="secondary" className="text-xs">
          {suggestions.length} insights
        </Badge>
      </div>

      {/* Suggestions Content */}
      <div className="space-y-3">
        {suggestions.map((suggestion) => {
          // Map suggestion types to icons
          const getIcon = (type: string) => {
            switch (type) {
              case 'cashback': return DollarSign;
              case 'spending': return CreditCard;
              case 'reward': return TrendingUp;
              case 'warning': return AlertTriangle;
              case 'goal': return Target;
              default: return Lightbulb;
            }
          };

          const IconComponent = getIcon(suggestion.type);
          return (
            <div
              key={suggestion.id}
              className="p-4 rounded-lg border border-gray-200 bg-white hover:shadow-sm transition-shadow"
            >
              <div className="flex items-start gap-3">
                <div className={`w-8 h-8 rounded-lg flex items-center justify-center ${
                  suggestion.priority === 'high' ? 'bg-red-50' :
                  suggestion.priority === 'medium' ? 'bg-amber-50' : 'bg-blue-50'
                }`}>
                  <IconComponent className={`h-4 w-4 ${
                    suggestion.priority === 'high' ? 'text-red-600' :
                    suggestion.priority === 'medium' ? 'text-amber-600' : 'text-blue-600'
                  }`} />
                </div>
                <div className="flex-1 min-w-0">
                  <div className="flex items-center justify-between mb-1">
                    <h4 className="font-semibold text-sm text-gray-900 truncate">{suggestion.title}</h4>
                    <Badge
                      variant={suggestion.priority === 'high' ? 'destructive' :
                              suggestion.priority === 'medium' ? 'default' : 'secondary'}
                      className="text-xs ml-2"
                    >
                      {suggestion.priority}
                    </Badge>
                  </div>
                  <p className="text-xs text-gray-600 mb-2 leading-relaxed">{suggestion.description}</p>
                  <div className="flex items-center justify-between">
                    <span className="text-xs text-gray-500 bg-gray-100 px-2 py-1 rounded">
                      {suggestion.category}
                    </span>
                    {suggestion.potentialSavings && (
                      <span className="text-sm font-semibold text-green-600">
                        +${suggestion.potentialSavings.toFixed(2)}
                      </span>
                    )}
                  </div>
                </div>
              </div>
            </div>
          );
        })}

        {/* Monthly Impact Summary */}
        <div className="mt-4 p-4 bg-green-50 border border-green-200 rounded-lg">
          <div className="flex items-center gap-2 mb-3">
            <div className="w-6 h-6 bg-green-100 rounded flex items-center justify-center">
              <TrendingUp className="h-3 w-3 text-green-600" />
            </div>
            <span className="font-semibold text-sm text-green-800">Monthly Impact</span>
          </div>
          <p className="text-sm text-gray-700 mb-2">
            Following our suggestions could save you{' '}
            <span className="font-bold text-green-700">
              ${suggestions.reduce((sum, s) => sum + (s.potentialSavings || 0), 0).toFixed(2)}
            </span>{' '}
            this month
          </p>
          <p className="text-xs text-gray-500">
            📈 Keep optimizing your spending patterns for maximum rewards!
          </p>
        </div>
      </div>
    </div>
  );
};

export default SmartSuggestions;
