
import React from 'react';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { Lightbulb, TrendingUp, AlertTriangle, DollarSign, CreditCard, Target, Calendar } from 'lucide-react';
import { useDateContext } from '../contexts/DateContext';
import { format } from 'date-fns';

const SmartSuggestions = () => {
  const { suggestions, selectedDate } = useDateContext();

  // Using suggestions from context instead of hardcoded data

  const getPriorityColor = (priority: string) => {
    switch (priority) {
      case 'high': return 'bg-gradient-to-r from-red-500 to-pink-500';
      case 'medium': return 'bg-gradient-to-r from-yellow-500 to-orange-500';
      case 'low': return 'bg-gradient-to-r from-blue-500 to-cyan-500';
      default: return 'bg-gray-500';
    }
  };

  const getPriorityBgColor = (priority: string, color: string) => {
    const baseColors = {
      high: 'bg-gradient-to-r from-red-50 to-pink-50 border-red-300',
      medium: 'bg-gradient-to-r from-yellow-50 to-orange-50 border-yellow-300',
      low: 'bg-gradient-to-r from-blue-50 to-cyan-50 border-blue-300'
    };
    return baseColors[priority as keyof typeof baseColors] || 'bg-gray-50 border-gray-200';
  };

  const getIconBgColor = (color: string) => {
    const colorMap = {
      green: 'bg-gradient-to-r from-green-100 to-emerald-100 text-green-700',
      yellow: 'bg-gradient-to-r from-yellow-100 to-amber-100 text-yellow-700',
      blue: 'bg-gradient-to-r from-blue-100 to-cyan-100 text-blue-700',
      red: 'bg-gradient-to-r from-red-100 to-pink-100 text-red-700',
      purple: 'bg-gradient-to-r from-purple-100 to-violet-100 text-purple-700'
    };
    return colorMap[color as keyof typeof colorMap] || 'bg-gray-100 text-gray-600';
  };

  return (
    <div className="space-y-6">
      {/* Cyberpunk Month Header */}
      <div className="relative group">
        <div className="absolute -inset-1 bg-gradient-to-r from-yellow-400 to-orange-500 rounded-2xl blur opacity-25 group-hover:opacity-75 transition duration-300 animate-gradient"></div>
        <div className="relative flex items-center justify-between p-4 bg-white/10 backdrop-blur-xl rounded-2xl border border-white/20">
          <div className="flex items-center gap-3">
            <div className="w-8 h-8 bg-gradient-to-r from-yellow-400 to-orange-500 rounded-lg flex items-center justify-center animate-pulse-glow">
              <Calendar className="h-4 w-4 text-white animate-bounce" />
            </div>
            <span className="text-sm font-bold text-white">
              {format(selectedDate, 'MMMM yyyy')}
            </span>
          </div>
          <div className="px-3 py-1 bg-gradient-to-r from-purple-400 to-pink-500 rounded-xl text-white text-xs font-bold animate-pulse-glow">
            {suggestions.length} AI INSIGHTS
          </div>
        </div>
      </div>

      {/* Neon Suggestions Grid */}
      <div className="space-y-4">
        {suggestions.map((suggestion, index) => {
          // Map suggestion types to icons
          const getIcon = (type: string) => {
            switch (type) {
              case 'cashback': return DollarSign;
              case 'spending': return CreditCard;
              case 'reward': return TrendingUp;
              case 'warning': return AlertTriangle;
              case 'goal': return Target;
              default: return Lightbulb;
            }
          };

          const IconComponent = getIcon(suggestion.type);

          const getGradientColors = (priority: string) => {
            switch (priority) {
              case 'high': return 'from-red-400 to-pink-500';
              case 'medium': return 'from-yellow-400 to-orange-500';
              default: return 'from-blue-400 to-cyan-500';
            }
          };

          return (
            <div
              key={suggestion.id}
              className={`group relative transition-all duration-500 delay-${index * 100}`}
              style={{ animationDelay: `${index * 0.1}s` }}
            >
              <div className={`absolute -inset-0.5 bg-gradient-to-r ${getGradientColors(suggestion.priority)} rounded-2xl blur opacity-25 group-hover:opacity-75 transition duration-300 animate-gradient`}></div>
              <div className="relative p-5 bg-white/10 backdrop-blur-xl rounded-2xl border border-white/20 transform hover:scale-105 transition-all duration-300">
                <div className="flex items-start gap-4">
                  <div className={`w-12 h-12 bg-gradient-to-r ${getGradientColors(suggestion.priority)} rounded-xl flex items-center justify-center shadow-lg animate-pulse-glow`}>
                    <IconComponent className="h-6 w-6 text-white" />
                  </div>
                  <div className="flex-1 min-w-0">
                    <div className="flex items-center justify-between mb-2">
                      <h4 className="font-bold text-white text-sm truncate">{suggestion.title}</h4>
                      <div className={`px-2 py-1 bg-gradient-to-r ${getGradientColors(suggestion.priority)} rounded-lg text-white text-xs font-bold animate-pulse-glow`}>
                        {suggestion.priority.toUpperCase()}
                      </div>
                    </div>
                    <p className="text-white/80 text-xs mb-3 leading-relaxed">{suggestion.description}</p>
                    <div className="flex items-center justify-between">
                      <span className="text-xs text-white/60 bg-white/10 px-3 py-1 rounded-lg border border-white/20">
                        {suggestion.category}
                      </span>
                      {suggestion.potentialSavings && (
                        <span className="text-sm font-bold bg-gradient-to-r from-green-400 to-emerald-400 bg-clip-text text-transparent animate-gradient">
                          +${suggestion.potentialSavings.toFixed(2)}
                        </span>
                      )}
                    </div>
                  </div>
                </div>
              </div>
            </div>
          );
        })}

        {/* Holographic Impact Summary */}
        <div className="relative group">
          <div className="absolute -inset-1 bg-gradient-to-r from-green-400 to-emerald-500 rounded-2xl blur opacity-25 group-hover:opacity-75 transition duration-300 animate-gradient"></div>
          <div className="relative p-6 bg-white/10 backdrop-blur-xl rounded-2xl border border-white/20">
            <div className="flex items-center gap-3 mb-4">
              <div className="w-10 h-10 bg-gradient-to-r from-green-400 to-emerald-500 rounded-xl flex items-center justify-center shadow-lg animate-pulse-glow">
                <TrendingUp className="h-5 w-5 text-white" />
              </div>
              <span className="font-bold text-white text-lg">AI IMPACT ANALYSIS</span>
            </div>
            <p className="text-white/90 text-sm mb-3 leading-relaxed">
              Following our AI suggestions could save you{' '}
              <span className="font-bold text-2xl bg-gradient-to-r from-green-400 to-emerald-400 bg-clip-text text-transparent animate-gradient">
                ${suggestions.reduce((sum, s) => sum + (s.potentialSavings || 0), 0).toFixed(2)}
              </span>{' '}
              this month
            </p>
            <div className="flex items-center gap-2 text-white/70 text-xs">
              <div className="w-2 h-2 bg-green-400 rounded-full animate-pulse"></div>
              <span>Neural network optimization active</span>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
};

export default SmartSuggestions;
