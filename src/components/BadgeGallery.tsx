// components/BadgeGallery.tsx
import React, { useEffect, useRef, useState } from "react";
import { Dialog } from "@headlessui/react";
import { X } from "lucide-react";
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { fireSpaghetti } from './SpaghettiPopper';

const badges = [
  {
    id: "big-spender",
    name: "Big Spender",
    count: 3,
    image: "https://img.icons8.com/color/96/shopping-cart-loaded.png",
    description: "You've spent over ₹50,000 this month. Treat yourself... or maybe slow down? 😅",
  },
  {
    id: "retail-royalty",
    name: "Retail Royalty",
    count: 2,
    image: "https://img.icons8.com/color/96/bag-front-view.png",
    description: "You made purchases at 5+ different retail stores this week. Shop till you drop!",
  },
  {
    id: "foodie-flash",
    name: "Foodie Flash",
    count: 1,
    image: "https://img.icons8.com/color/96/hamburger.png",
    description: "You've had 10+ food delivery or restaurant transactions this month. Delicious!",
  },
  {
    id: "jet-setter",
    name: "<PERSON> Setter",
    image: "https://img.icons8.com/color/96/airport.png",
    description: "Spent on flights or travel bookings this month. Where to next? 🌍",
  },
  {
    id: "fuel-friend",
    name: "Fuel Friend",
    count: 1,
    image: "https://img.icons8.com/color/96/gas-station.png",
    description: "Used your card 3+ times at fuel stations. You're keeping the engine running!",
  },
  {
    id: "bill-boss",
    name: "Bill Boss",
    image: "https://img.icons8.com/color/96/invoice.png",
    description: "Paid utility, phone, or DTH bills on time for 3+ months in a row. Respect.",
  },
  {
    id: "reward-hunter",
    name: "Reward Hunter",
    count: 2,
    image: "https://img.icons8.com/color/96/gift.png",
    description: "Redeemed your reward points this month. Smart move!",
  },
  {
    id: "smart-saver",
    name: "Smart Saver",
    image: "https://img.icons8.com/color/96/money-box.png",
    description: "You’ve spent less than last month. Keep up the great control! 💪",
  },
  {
    id: "category-crusher",
    name: "Category Crusher",
    image: "https://img.icons8.com/color/96/target.png",
    description: "Top spender in one category: dining, travel, or shopping. Consistency!",
  },
  {
    id: "card-commander",
    name: "Card Commander",
    count: 1,
    image: "https://img.icons8.com/color/96/bank-cards.png",
    description: "Used your credit card for 90% of your purchases. Commander-level loyalty!",
  },
];

export default function BadgeGallery() {
  const sectionRef = useRef<HTMLDivElement>(null);
  const hasFired = useRef(false); // ensure it only fires once

  useEffect(() => {
    const observer = new IntersectionObserver(
      ([entry]) => {
        if (entry.isIntersecting && !hasFired.current) {
          fireSpaghetti();
        }
      },
      {
        threshold: 0.5, // fire when 50% of section is visible
      }
    );

    if (sectionRef.current) {
      observer.observe(sectionRef.current);
    }

    return () => {
      if (sectionRef.current) observer.unobserve(sectionRef.current);
    };
  }, []);

  const [selected, setSelected] = useState(null);

    return (
      <div ref={sectionRef}>
            <Card className="w-full bg-gradient-to-br from-blue-50 to-purple-50 border-blue-200">
      <CardHeader className="bg-gradient-to-r from-blue-600 to-purple-600 text-white rounded-t-lg">
                    <CardTitle className="flex items-center gap-2">
                        Badges
                    </CardTitle>
                </CardHeader>
                <CardContent>
    <div className="p-6">
      <div className="grid grid-cols-2 sm:grid-cols-3 md:grid-cols-4 gap-6">
        {badges.map((badge) => (
          <div
            key={badge.id}
            className="text-center cursor-pointer relative group"
            onClick={() => setSelected(badge)}
          >
            <img
              src={badge.image}
              alt={badge.name}
              className="w-20 h-20 mx-auto transition-transform duration-200 group-hover:scale-105"
            />
            <p className="font-medium mt-2 text-gray-700">{badge.name}</p>
          </div>
        ))}
      </div>

      {/* Modal */}
      <Dialog open={!!selected} onClose={() => setSelected(null)} className="relative z-50">
        <div className="fixed inset-0 bg-black/30 backdrop-blur-sm" aria-hidden="true" />
        <div className="fixed inset-0 flex items-center justify-center p-4">
          <Dialog.Panel className="bg-white p-6 rounded-lg shadow-lg max-w-sm w-full relative">
            <button
              className="absolute top-2 right-2 text-gray-500 hover:text-red-500"
              onClick={() => setSelected(null)}
            >
              <X className="w-5 h-5" />
            </button>
            {selected && (
              <>
                <img
                  src={selected.image}
                  alt={selected.name}
                  className="w-24 h-24 mx-auto mb-4"
                />
                <Dialog.Title className="text-xl font-bold text-center text-gray-800">
                  {selected.name}
                </Dialog.Title>
                <Dialog.Description className="text-center text-gray-600 mt-2">
                  {selected.description}
                </Dialog.Description>
              </>
            )}
          </Dialog.Panel>
        </div>
      </Dialog>
    </div>
                </CardContent>
            
            </Card>

      </div>
  );
}
