import React from 'react';
import { Wifi, CreditCard, Shield, Star } from 'lucide-react';

interface CardData {
  cardNumber: string;
  cardholderName: string;
  expiryDate: string;
  cardType: 'VISA' | 'MASTERCARD' | 'AMEX';
  bankName: string;
  balance: number;
  creditLimit: number;
  rewardPoints: number;
}

const CreditCardHeader: React.FC = () => {
  const cardData: CardData = {
    cardNumber: '**** **** **** 8542',
    cardholderName: 'JOHN SMITH',
    expiryDate: '12/28',
    cardType: 'VISA',
    bankName: 'Premier Bank',
    balance: 2847.50,
    creditLimit: 15000,
    rewardPoints: 12450
  };

  const getCardGradient = (type: string) => {
    switch (type) {
      case 'VISA':
        return 'from-blue-600 via-blue-700 to-blue-800';
      case 'MASTERCARD':
        return 'from-red-600 via-red-700 to-red-800';
      case 'AMEX':
        return 'from-green-600 via-green-700 to-green-800';
      default:
        return 'from-slate-600 via-slate-700 to-slate-800';
    }
  };

  const utilizationPercentage = (cardData.balance / cardData.creditLimit) * 100;

  return (
    <div className="w-full max-w-md mx-auto">
      {/* Professional Credit Card Design */}
      <div className="relative group">
        {/* Subtle glow effect */}
        <div className="absolute -inset-1 bg-gradient-to-r from-blue-600/20 to-indigo-600/20 rounded-2xl blur-sm opacity-75 group-hover:opacity-100 transition duration-300"></div>

        {/* Main card */}
        <div className={`relative bg-gradient-to-br ${getCardGradient(cardData.cardType)} rounded-2xl p-6 text-white shadow-xl transform hover:scale-[1.02] transition-all duration-300`}>
          {/* Subtle pattern overlay */}
          <div className="absolute inset-0 bg-gradient-to-br from-white/5 to-transparent rounded-2xl"></div>

          {/* Card header */}
          <div className="relative z-10 flex justify-between items-start mb-6">
            <div>
              <p className="text-white/80 text-sm font-medium mb-1">{cardData.bankName}</p>
              <p className="text-white text-lg font-semibold">Premium Credit Card</p>
            </div>
            <div className="flex items-center gap-3">
              <div className="flex items-center gap-1">
                <Shield className="h-4 w-4 text-white/70" />
                <span className="text-xs text-white/70">Secured</span>
              </div>
              <Wifi className="h-5 w-5 text-white/70" />
              <span className="text-white font-bold text-lg">{cardData.cardType}</span>
            </div>
          </div>

          {/* EMV Chip */}
          <div className="relative z-10 mb-6">
            <div className="w-12 h-8 bg-gradient-to-br from-yellow-400 to-yellow-600 rounded-md flex items-center justify-center shadow-md">
              <div className="w-8 h-5 bg-gradient-to-br from-yellow-300 to-yellow-500 rounded-sm"></div>
            </div>
          </div>

          {/* Card number */}
          <div className="relative z-10 mb-6">
            <p className="text-white text-xl font-mono tracking-wider font-medium">
              {cardData.cardNumber}
            </p>
          </div>

          {/* Card details */}
          <div className="relative z-10 flex justify-between items-end">
            <div>
              <p className="text-white/60 text-xs uppercase tracking-wide mb-1">Card Holder</p>
              <p className="text-white text-sm font-medium">{cardData.cardholderName}</p>
            </div>
            <div className="text-right">
              <p className="text-white/60 text-xs uppercase tracking-wide mb-1">Expires</p>
              <p className="text-white text-sm font-medium">{cardData.expiryDate}</p>
            </div>
          </div>

          {/* Decorative elements */}
          <div className="absolute bottom-0 right-0 w-20 h-20 bg-gradient-to-tl from-white/5 to-transparent rounded-full transform translate-x-10 translate-y-10"></div>
        </div>
      </div>

      {/* Professional Card Stats */}
      <div className="mt-4 grid grid-cols-3 gap-3">
        {/* Balance */}
        <div className="bg-white rounded-xl p-4 shadow-sm border border-gray-100 hover:shadow-md transition-shadow duration-200">
          <div className="flex items-center gap-2 mb-2">
            <CreditCard className="h-4 w-4 text-blue-600" />
            <span className="text-xs font-medium text-gray-600 uppercase tracking-wide">Balance</span>
          </div>
          <p className="text-lg font-bold text-gray-900">${cardData.balance.toLocaleString()}</p>
          <div className="mt-2">
            <div className="flex justify-between text-xs text-gray-500 mb-1">
              <span>Utilization</span>
              <span>{utilizationPercentage.toFixed(1)}%</span>
            </div>
            <div className="w-full bg-gray-200 rounded-full h-1.5">
              <div
                className={`h-1.5 rounded-full transition-all duration-300 ${
                  utilizationPercentage > 80 ? 'bg-red-500' :
                  utilizationPercentage > 50 ? 'bg-yellow-500' : 'bg-green-500'
                }`}
                style={{ width: `${Math.min(utilizationPercentage, 100)}%` }}
              ></div>
            </div>
          </div>
        </div>

        {/* Credit Limit */}
        <div className="bg-white rounded-xl p-4 shadow-sm border border-gray-100 hover:shadow-md transition-shadow duration-200">
          <div className="flex items-center gap-2 mb-2">
            <Shield className="h-4 w-4 text-green-600" />
            <span className="text-xs font-medium text-gray-600 uppercase tracking-wide">Limit</span>
          </div>
          <p className="text-lg font-bold text-gray-900">${cardData.creditLimit.toLocaleString()}</p>
          <p className="text-xs text-gray-500 mt-1">
            Available: ${(cardData.creditLimit - cardData.balance).toLocaleString()}
          </p>
        </div>

        {/* Reward Points */}
        <div className="bg-white rounded-xl p-4 shadow-sm border border-gray-100 hover:shadow-md transition-shadow duration-200">
          <div className="flex items-center gap-2 mb-2">
            <Star className="h-4 w-4 text-yellow-600" />
            <span className="text-xs font-medium text-gray-600 uppercase tracking-wide">Rewards</span>
          </div>
          <p className="text-lg font-bold text-gray-900">{cardData.rewardPoints.toLocaleString()}</p>
          <p className="text-xs text-gray-500 mt-1">
            ≈ ${(cardData.rewardPoints * 0.01).toFixed(0)} value
          </p>
        </div>
      </div>
    </div>
  );
};

export default CreditCardHeader;
