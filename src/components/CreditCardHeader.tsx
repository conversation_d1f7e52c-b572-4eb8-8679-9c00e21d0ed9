
import React from 'react';
import { Card, CardContent } from '@/components/ui/card';
import { CreditCard, Wifi } from 'lucide-react';

const CreditCardHeader = () => {
  const cardData = {
    cardNumber: '**** **** **** 8542',
    cardholderName: 'RAJESH KUMAR',
    expiryDate: '12/28',
    cardType: 'VISA',
    bankName: 'HDFC Bank'
  };

  return (
    <div className="mb-8">
      <Card className="main-credit-card relative overflow-hidden bg-gradient-to-br from-slate-900 via-purple-900 to-blue-900 border-0 shadow-2xl">
        <CardContent className="p-8">
          {/* Background Pattern */}
          <div className="absolute inset-0 bg-gradient-to-r from-transparent via-white/5 to-transparent transform -skew-y-6"></div>
          <div className="absolute top-4 right-4 opacity-10">
            <div className="w-32 h-32 rounded-full border-2 border-white/20"></div>
            <div className="absolute top-8 left-8 w-16 h-16 rounded-full border-2 border-white/30"></div>
          </div>
          
          <div className="relative z-10">
            {/* Header Row */}
            <div className="flex justify-between items-start mb-8">
              <div>
                <h3 className="text-white/80 text-sm font-medium mb-1">{cardData.bankName}</h3>
                <p className="text-white text-lg font-bold">Credit Card</p>
              </div>
              <div className="flex items-center gap-2">
                <Wifi className="h-5 w-5 text-white/60" />
                <div className="text-white font-bold text-lg">{}</div>
              </div>
            </div>

            {/* Chip */}
            <div className="mb-8">
              <div className="w-12 h-9 bg-gradient-to-br from-yellow-400 to-yellow-600 rounded-lg flex items-center justify-center shadow-lg">
                <div className="w-8 h-6 bg-gradient-to-br from-yellow-300 to-yellow-500 rounded-sm"></div>
              </div>
            </div>

            {/* Card Number */}
            <div className=" mb-6">
              <p className="card-number // SpaghettiPopper.ts
import confetti from 'canvas-confetti';

// Fire multiple bursts from bottom to top (100% screen height)
export const fireSpaghettiFullScreen = () => {
  const numBursts = 8;

  for (let i = 0; i < numBursts; i++) {
    const delay = i * 100; // staggered bursts
    const verticalOrigin = 1 - i / (numBursts - 1); // 1 to 0 (bottom to top)

    setTimeout(() => {
      confetti({
        particleCount: 50,
        angle: 90,
        spread: 140,
        origin: { y: verticalOrigin }, // spread vertically
        startVelocity: 45,
        colors: ['#f39c12', '#e74c3c', '#27ae60', '#8e44ad', '#f1c40f'],
        shapes: ['square'],
      });
    }, delay);
  }
};
text-white text-2xl font-mono tracking-wider font-medium">
                {cardData.cardNumber}
              </p>
            </div>

            {/* Bottom Row */}
            <div className="flex justify-between items-end">
              <div>
                <p className="text-white/60 text-xs uppercase tracking-wide mb-1">Card Holder</p>
                <p className="text-white text-sm font-medium tracking-wide">{cardData.cardholderName}</p>
              </div>
              <div className="text-right">
                <p className="text-white/60 text-xs uppercase tracking-wide mb-1">Expires</p>
                <p className="text-white text-sm font-medium">{cardData.expiryDate}</p>
              </div>
            </div>
          </div>
        </div>

        {/* Chip */}
        <div className="mb-6">
          <div className="w-10 h-7 bg-gradient-to-br from-yellow-400 to-yellow-600 rounded-md flex items-center justify-center">
            <div className="w-6 h-4 bg-gradient-to-br from-yellow-300 to-yellow-500 rounded-sm"></div>
          </div>
        </div>

        {/* Card Number */}
        <div className="mb-4">
          <p className="text-white text-lg font-mono tracking-wider">
            {cardData.cardNumber}
          </p>
        </div>

        {/* Bottom Row */}
        <div className="flex justify-between items-end">
          <div>
            <p className="text-white/60 text-xs uppercase tracking-wide mb-1">Card Holder</p>
            <p className="text-white text-xs font-medium">{cardData.cardholderName}</p>
          </div>
          <div className="text-right">
            <p className="text-white/60 text-xs uppercase tracking-wide mb-1">Expires</p>
            <p className="text-white text-xs font-medium">{cardData.expiryDate}</p>
          </div>
        </div>
      </div>

      {/* Decorative Elements */}
      <div className="absolute bottom-0 right-0 w-16 h-16 bg-gradient-to-tl from-white/10 to-transparent rounded-full transform translate-x-8 translate-y-8"></div>
    </div>
  );
};

export default CreditCardHeader;
