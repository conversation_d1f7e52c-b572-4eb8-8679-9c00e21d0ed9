import React from 'react';
import { Wifi, CreditCard, Shield, TrendingUp, AlertCircle, CheckCircle } from 'lucide-react';

interface CardData {
  cardNumber: string;
  cardholderName: string;
  expiryDate: string;
  cardType: 'VISA' | 'MASTERCARD' | 'AMEX';
  bankName: string;
  balance: number;
  creditLimit: number;
  availableCredit: number;
  lastPayment: number;
  nextPaymentDue: string;
  accountStatus: 'active' | 'warning' | 'overdue';
}

interface CreditCardHeaderProps {
  className?: string;
}

const CreditCardHeader: React.FC<CreditCardHeaderProps> = ({ className = '' }) => {
  const cardData: CardData = {
    cardNumber: '**** **** **** 8542',
    cardholderName: '<PERSON>',
    expiryDate: '12/28',
    cardType: 'VISA',
    bankName: 'Premier Financial',
    balance: 2847.50,
    creditLimit: 15000,
    availableCredit: 12152.50,
    lastPayment: 450.00,
    nextPaymentDue: 'Dec 15, 2024',
    accountStatus: 'active'
  };

  const utilizationPercentage = (cardData.balance / cardData.creditLimit) * 100;

  const getStatusColor = (status: string) => {
    switch (status) {
      case 'active': return 'text-green-600';
      case 'warning': return 'text-yellow-600';
      case 'overdue': return 'text-red-600';
      default: return 'text-gray-600';
    }
  };

  const getStatusIcon = (status: string) => {
    switch (status) {
      case 'active': return CheckCircle;
      case 'warning': return AlertCircle;
      case 'overdue': return AlertCircle;
      default: return CheckCircle;
    }
  };

  const StatusIcon = getStatusIcon(cardData.accountStatus);

  return (
    <div className={`bg-white rounded-lg shadow-sm border border-gray-200 ${className}`}>
      {/* Header Section */}
      <div className="p-6 border-b border-gray-100">
        <div className="flex items-center justify-between mb-4">
          <div>
            <h2 className="text-lg font-semibold text-gray-900">Credit Card Overview</h2>
            <p className="text-sm text-gray-600">{cardData.bankName} • {cardData.cardType}</p>
          </div>
          <div className="flex items-center gap-2">
            <StatusIcon className={`h-4 w-4 ${getStatusColor(cardData.accountStatus)}`} />
            <span className={`text-sm font-medium capitalize ${getStatusColor(cardData.accountStatus)}`}>
              {cardData.accountStatus}
            </span>
          </div>
        </div>

        {/* Card Visual Representation */}
        <div className="bg-gradient-to-r from-slate-800 to-slate-900 rounded-lg p-6 text-white relative overflow-hidden">
          {/* Subtle pattern */}
          <div className="absolute inset-0 opacity-10">
            <div className="absolute top-0 right-0 w-32 h-32 bg-white rounded-full transform translate-x-16 -translate-y-16"></div>
            <div className="absolute bottom-0 left-0 w-24 h-24 bg-white rounded-full transform -translate-x-12 translate-y-12"></div>
          </div>

          <div className="relative z-10">
            {/* Card Header */}
            <div className="flex justify-between items-start mb-8">
              <div>
                <p className="text-white/80 text-sm font-medium">{cardData.bankName}</p>
                <p className="text-white text-base font-semibold">Premium Card</p>
              </div>
              <div className="flex items-center gap-3">
                <Wifi className="h-4 w-4 text-white/60" />
                <span className="text-white font-semibold text-sm">{cardData.cardType}</span>
              </div>
            </div>

            {/* Chip */}
            <div className="w-10 h-7 bg-gradient-to-br from-yellow-400 to-yellow-600 rounded-md mb-8 flex items-center justify-center">
              <div className="w-6 h-4 bg-gradient-to-br from-yellow-300 to-yellow-500 rounded-sm"></div>
            </div>

            {/* Card Number */}
            <div className="mb-6">
              <p className="text-white text-lg font-mono tracking-wider">
                {cardData.cardNumber}
              </p>
            </div>

            {/* Card Details */}
            <div className="flex justify-between items-end">
              <div>
                <p className="text-white/60 text-xs uppercase tracking-wide mb-1">Cardholder</p>
                <p className="text-white text-sm font-medium">{cardData.cardholderName}</p>
              </div>
              <div className="text-right">
                <p className="text-white/60 text-xs uppercase tracking-wide mb-1">Expires</p>
                <p className="text-white text-sm font-medium">{cardData.expiryDate}</p>
              </div>
            </div>
          </div>
        </div>
      </div>

      {/* Account Summary */}
      <div className="p-6">
        <h3 className="text-base font-semibold text-gray-900 mb-4">Account Summary</h3>

        <div className="grid grid-cols-2 lg:grid-cols-4 gap-4 mb-6">
          {/* Current Balance */}
          <div className="bg-gray-50 rounded-lg p-4">
            <div className="flex items-center gap-2 mb-2">
              <CreditCard className="h-4 w-4 text-gray-600" />
              <span className="text-xs font-medium text-gray-600 uppercase tracking-wide">Current Balance</span>
            </div>
            <p className="text-xl font-bold text-gray-900">${cardData.balance.toLocaleString()}</p>
          </div>

          {/* Available Credit */}
          <div className="bg-gray-50 rounded-lg p-4">
            <div className="flex items-center gap-2 mb-2">
              <Shield className="h-4 w-4 text-green-600" />
              <span className="text-xs font-medium text-gray-600 uppercase tracking-wide">Available Credit</span>
            </div>
            <p className="text-xl font-bold text-green-600">${cardData.availableCredit.toLocaleString()}</p>
          </div>

          {/* Credit Limit */}
          <div className="bg-gray-50 rounded-lg p-4">
            <div className="flex items-center gap-2 mb-2">
              <TrendingUp className="h-4 w-4 text-blue-600" />
              <span className="text-xs font-medium text-gray-600 uppercase tracking-wide">Credit Limit</span>
            </div>
            <p className="text-xl font-bold text-gray-900">${cardData.creditLimit.toLocaleString()}</p>
          </div>

          {/* Last Payment */}
          <div className="bg-gray-50 rounded-lg p-4">
            <div className="flex items-center gap-2 mb-2">
              <CheckCircle className="h-4 w-4 text-green-600" />
              <span className="text-xs font-medium text-gray-600 uppercase tracking-wide">Last Payment</span>
            </div>
            <p className="text-xl font-bold text-gray-900">${cardData.lastPayment.toLocaleString()}</p>
          </div>
        </div>

        {/* Credit Utilization */}
        <div className="mb-4">
          <div className="flex justify-between items-center mb-2">
            <span className="text-sm font-medium text-gray-700">Credit Utilization</span>
            <span className="text-sm font-semibold text-gray-900">{utilizationPercentage.toFixed(1)}%</span>
          </div>
          <div className="w-full bg-gray-200 rounded-full h-2">
            <div
              className={`h-2 rounded-full transition-all duration-500 ${
                utilizationPercentage > 80 ? 'bg-red-500' :
                utilizationPercentage > 50 ? 'bg-yellow-500' : 'bg-green-500'
              }`}
              style={{ width: `${Math.min(utilizationPercentage, 100)}%` }}
            ></div>
          </div>
          <p className="text-xs text-gray-500 mt-1">
            {utilizationPercentage < 30 ? 'Excellent utilization ratio' :
             utilizationPercentage < 50 ? 'Good utilization ratio' :
             utilizationPercentage < 80 ? 'Consider paying down balance' :
             'High utilization - pay down immediately'}
          </p>
        </div>

        {/* Next Payment Due */}
        <div className="bg-blue-50 border border-blue-200 rounded-lg p-4">
          <div className="flex items-center justify-between">
            <div>
              <p className="text-sm font-medium text-blue-900">Next Payment Due</p>
              <p className="text-lg font-bold text-blue-900">{cardData.nextPaymentDue}</p>
            </div>
            <button className="bg-blue-600 hover:bg-blue-700 text-white px-4 py-2 rounded-lg text-sm font-medium transition-colors">
              Make Payment
            </button>
          </div>
        </div>
      </div>
    </div>
  );
};

export default CreditCardHeader;
