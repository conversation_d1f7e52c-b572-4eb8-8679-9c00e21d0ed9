
import React from 'react';
import { CreditCard, Wifi } from 'lucide-react';

const CreditCardHeader = () => {
  const cardData = {
    cardNumber: '**** **** **** 8542',
    cardholderName: 'JOHN SMITH',
    expiryDate: '12/28',
    cardType: 'VISA',
    bankName: 'Premier Bank'
  };

  return (
    <div className="relative group" style={{ perspective: '1000px' }}>
      <div className="absolute -inset-2 bg-gradient-to-r from-cyan-400 via-purple-500 to-pink-500 rounded-3xl blur-lg opacity-30 group-hover:opacity-60 transition duration-500 animate-gradient"></div>

      <div className="relative bg-gradient-to-br from-slate-900 via-purple-900 to-blue-900 rounded-3xl p-8 text-white overflow-hidden shadow-2xl transform hover:scale-105 hover:rotate-1 transition-all duration-500" style={{ transformStyle: 'preserve-3d' }}>
        <div className="absolute inset-0 bg-gradient-to-r from-transparent via-white/10 to-transparent transform -skew-y-12 animate-shimmer"></div>

        <div className="absolute inset-0 overflow-hidden">
          {[...Array(8)].map((_, i) => (
            <div
              key={i}
              className="absolute w-1 h-1 bg-white/30 rounded-full animate-float"
              style={{
                left: `${Math.random() * 100}%`,
                top: `${Math.random() * 100}%`,
                animationDelay: `${Math.random() * 3}s`,
                animationDuration: `${2 + Math.random() * 3}s`
              }}
            />
          ))}
        </div>

        <div className="absolute top-4 right-4 opacity-20">
          <div className="w-32 h-32 rounded-full border-2 border-cyan-400/50 animate-pulse-glow"></div>
          <div className="absolute top-8 left-8 w-16 h-16 rounded-full border-2 border-purple-400/50 animate-pulse-glow animation-delay-2000"></div>
          <div className="absolute top-12 left-12 w-8 h-8 rounded-full border-2 border-pink-400/50 animate-pulse-glow animation-delay-4000"></div>
        </div>

        <div className="relative z-10">
          <div className="flex justify-between items-start mb-8">
            <div>
              <p className="text-cyan-400 text-sm font-bold mb-2 animate-neon-flicker">{cardData.bankName}</p>
              <p className="text-white text-lg font-bold tracking-wide">QUANTUM CREDIT CARD</p>
            </div>
            <div className="flex items-center gap-3">
              <Wifi className="h-6 w-6 text-cyan-400 animate-pulse" />
              <span className="text-white font-bold text-lg bg-gradient-to-r from-cyan-400 to-purple-400 bg-clip-text text-transparent animate-gradient">
                {cardData.cardType}
              </span>
            </div>
          </div>

          <div className="mb-8">
            <div className="w-14 h-10 bg-gradient-to-br from-yellow-400 via-orange-500 to-red-500 rounded-lg flex items-center justify-center shadow-lg transform hover:scale-110 transition-transform duration-300 animate-pulse-glow">
              <div className="w-10 h-6 bg-gradient-to-br from-yellow-300 to-orange-400 rounded-md animate-shimmer"></div>
            </div>
          </div>

          <div className="mb-6">
            <p className="text-white text-2xl font-mono tracking-widest font-bold animate-neon-flicker">
              {cardData.cardNumber}
            </p>
          </div>

          <div className="flex justify-between items-end">
            <div className="group">
              <p className="text-cyan-400/80 text-xs uppercase tracking-wider mb-2 font-semibold">Card Holder</p>
              <p className="text-white text-sm font-bold tracking-wide group-hover:text-cyan-400 transition-colors duration-300">
                {cardData.cardholderName}
              </p>
            </div>
            <div className="text-right group">
              <p className="text-purple-400/80 text-xs uppercase tracking-wider mb-2 font-semibold">Expires</p>
              <p className="text-white text-sm font-bold group-hover:text-purple-400 transition-colors duration-300">
                {cardData.expiryDate}
              </p>
            </div>
          </div>
        </div>

        <div className="absolute bottom-0 right-0 w-24 h-24 bg-gradient-to-tl from-cyan-400/20 via-purple-400/20 to-transparent rounded-full transform translate-x-12 translate-y-12 animate-pulse-glow"></div>
        <div className="absolute top-0 left-0 w-16 h-16 bg-gradient-to-br from-pink-400/20 via-purple-400/20 to-transparent rounded-full transform -translate-x-8 -translate-y-8 animate-pulse-glow animation-delay-2000"></div>
      </div>
    </div>
  );
};

export default CreditCardHeader;
        {/* Animated Background Pattern */}
        <div className="absolute inset-0 bg-gradient-to-r from-transparent via-white/10 to-transparent transform -skew-y-12 animate-shimmer"></div>

        {/* Floating Particles */}
        <div className="absolute inset-0 overflow-hidden">
          {[...Array(8)].map((_, i) => (
            <div
              key={i}
              className="absolute w-1 h-1 bg-white/30 rounded-full animate-float"
              style={{
                left: `${Math.random() * 100}%`,
                top: `${Math.random() * 100}%`,
                animationDelay: `${Math.random() * 3}s`,
                animationDuration: `${2 + Math.random() * 3}s`
              }}
            />
          ))}
        </div>

        {/* Neon Circuit Pattern */}
        <div className="absolute top-4 right-4 opacity-20">
          <div className="w-32 h-32 rounded-full border-2 border-cyan-400/50 animate-pulse-glow"></div>
          <div className="absolute top-8 left-8 w-16 h-16 rounded-full border-2 border-purple-400/50 animate-pulse-glow animation-delay-2000"></div>
          <div className="absolute top-12 left-12 w-8 h-8 rounded-full border-2 border-pink-400/50 animate-pulse-glow animation-delay-4000"></div>
        </div>

        <div className="relative z-10">
          {/* Header Row */}
          <div className="flex justify-between items-start mb-8">
            <div>
              <p className="text-cyan-400 text-sm font-bold mb-2 animate-neon-flicker">{cardData.bankName}</p>
              <p className="text-white text-lg font-bold tracking-wide">QUANTUM CREDIT CARD</p>
            </div>
            <div className="flex items-center gap-3">
              <Wifi className="h-6 w-6 text-cyan-400 animate-pulse" />
              <span className="text-white font-bold text-lg bg-gradient-to-r from-cyan-400 to-purple-400 bg-clip-text text-transparent animate-gradient">
                {cardData.cardType}
              </span>
            </div>
          </div>

          {/* Holographic Chip */}
          <div className="mb-8">
            <div className="w-14 h-10 bg-gradient-to-br from-yellow-400 via-orange-500 to-red-500 rounded-lg flex items-center justify-center shadow-lg transform hover:scale-110 transition-transform duration-300 animate-pulse-glow">
              <div className="w-10 h-6 bg-gradient-to-br from-yellow-300 to-orange-400 rounded-md animate-shimmer"></div>
            </div>
          </div>

          {/* Neon Card Number */}
          <div className="mb-6">
            <p className="text-white text-2xl font-mono tracking-widest font-bold animate-neon-flicker">
              {cardData.cardNumber}
            </p>
          </div>

          {/* Bottom Row with Glow Effects */}
          <div className="flex justify-between items-end">
            <div className="group">
                <p className="text-cyan-400/80 text-xs uppercase tracking-wider mb-2 font-semibold">Card Holder</p>
              <p className="text-white text-sm font-bold tracking-wide group-hover:text-cyan-400 transition-colors duration-300">
                {cardData.cardholderName}
              </p>
            </div>
            <div className="text-right group">
              <p className="text-purple-400/80 text-xs uppercase tracking-wider mb-2 font-semibold">Expires</p>
              <p className="text-white text-sm font-bold group-hover:text-purple-400 transition-colors duration-300">
                {cardData.expiryDate}
              </p>
            </div>
          </div>
      </div>

      {/* Holographic Decorative Elements */}
      <div className="absolute bottom-0 right-0 w-24 h-24 bg-gradient-to-tl from-cyan-400/20 via-purple-400/20 to-transparent rounded-full transform translate-x-12 translate-y-12 animate-pulse-glow"></div>
      <div className="absolute top-0 left-0 w-16 h-16 bg-gradient-to-br from-pink-400/20 via-purple-400/20 to-transparent rounded-full transform -translate-x-8 -translate-y-8 animate-pulse-glow animation-delay-2000"></div>
    </div>
  );
};

export default CreditCardHeader;
