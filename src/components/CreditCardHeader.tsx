
import React from 'react';
import { Card, CardContent } from '@/components/ui/card';
import { CreditCard, Wifi } from 'lucide-react';

const CreditCardHeader = () => {
  const cardData = {
    cardNumber: '**** **** **** 8542',
    cardholderName: 'JOHN SMITH',
    expiryDate: '12/28',
    cardType: 'VISA',
    bankName: 'Premier Bank'
  };

  return (
    <div className="mx-[20%] bg-gradient-to-r from-gray-900 via-blue-900 to-purple-900 rounded-xl p-6 text-white relative overflow-hidden">
      {/* Background Pattern */}
      <div className="absolute inset-0 bg-gradient-to-r from-transparent via-white/5 to-transparent transform -skew-y-6"></div>
      <div className="absolute top-4 right-4 opacity-10">
        <div className="w-24 h-24 rounded-full border border-white/20"></div>
        <div className="absolute top-6 left-6 w-12 h-12 rounded-full border border-white/30"></div>
      </div>

      <div className="relative z-10">
        {/* Header Row */}
        <div className="flex justify-between items-start mb-6">
          <div>
            <p className="text-white/70 text-xs font-medium mb-1">{cardData.bankName}</p>
            <p className="text-white text-sm font-semibold">Credit Card</p>
          </div>
          <div className="flex items-center gap-2">
            <Wifi className="h-4 w-4 text-white/60" />
            <span className="text-white font-semibold text-sm">{cardData.cardType}</span>
          </div>
        </div>

        {/* Chip */}
        <div className="mb-6">
          <div className="w-10 h-7 bg-gradient-to-br from-yellow-400 to-yellow-600 rounded-md flex items-center justify-center">
            <div className="w-6 h-4 bg-gradient-to-br from-yellow-300 to-yellow-500 rounded-sm"></div>
          </div>
        </div>

        {/* Card Number */}
        <div className="mb-4">
          <p className="text-white text-lg font-mono tracking-wider">
            {cardData.cardNumber}
          </p>
        </div>

        {/* Bottom Row */}
        <div className="flex justify-between items-end">
          <div>
            <p className="text-white/60 text-xs uppercase tracking-wide mb-1">Card Holder</p>
            <p className="text-white text-xs font-medium">{cardData.cardholderName}</p>
          </div>
          <div className="text-right">
            <p className="text-white/60 text-xs uppercase tracking-wide mb-1">Expires</p>
            <p className="text-white text-xs font-medium">{cardData.expiryDate}</p>
          </div>
        </div>
      </div>

      {/* Decorative Elements */}
      <div className="absolute bottom-0 right-0 w-16 h-16 bg-gradient-to-tl from-white/10 to-transparent rounded-full transform translate-x-8 translate-y-8"></div>
    </div>
  );
};

export default CreditCardHeader;
