import React from 'react';

interface CardData {
  cardNumber: string;
  cardholderName: string;
  expiryDate: string;
  cardType: string;
  bankName: string;
  balance: number;
  creditLimit: number;
  availableCredit: number;
  lastPayment: number;
  nextPaymentDue: string;
  accountStatus: string;
}

interface CreditCardHeaderProps {
  className?: string;
}

const CreditCardHeader: React.FC<CreditCardHeaderProps> = ({ className = '' }) => {
  const cardData: CardData = {
    cardNumber: '**** **** **** 8542',
    cardholderName: '<PERSON>',
    expiryDate: '12/28',
    cardType: 'VISA',
    bankName: 'Premier Bank',
    balance: 2847.50,
    creditLimit: 15000,
    availableCredit: 12152.50,
    lastPayment: 450.00,
    nextPaymentDue: 'Dec 15, 2024',
    accountStatus: 'Active'
  };

  const utilizationPercentage = (cardData.balance / cardData.creditLimit) * 100;

  return (
    <div className={`bg-white border border-gray-300 ${className}`}>
      {/* Simple Header */}
      <div className="px-4 py-3 border-b border-gray-300 bg-gray-50">
        <h2 className="text-sm font-medium text-gray-900">Credit Card Account</h2>
      </div>

      {/* Card Information */}
      <div className="p-4">
        <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
          {/* Card Details */}
          <div>
            <div className="bg-gray-100 border border-gray-300 p-4 mb-4">
              <div className="flex justify-between items-start mb-4">
                <div>
                  <p className="text-xs text-gray-600">{cardData.bankName}</p>
                  <p className="text-sm font-medium text-gray-900">{cardData.cardType} Credit Card</p>
                </div>
                <span className="text-xs text-gray-600">{cardData.accountStatus}</span>
              </div>

              <div className="mb-4">
                <p className="text-sm font-mono text-gray-900">{cardData.cardNumber}</p>
              </div>

              <div className="flex justify-between">
                <div>
                  <p className="text-xs text-gray-600">Cardholder</p>
                  <p className="text-sm text-gray-900">{cardData.cardholderName}</p>
                </div>
                <div className="text-right">
                  <p className="text-xs text-gray-600">Expires</p>
                  <p className="text-sm text-gray-900">{cardData.expiryDate}</p>
                </div>
              </div>
            </div>
          </div>

          {/* Account Summary */}
          <div>
            <table className="w-full text-sm">
              <tbody className="divide-y divide-gray-200">
                <tr>
                  <td className="py-2 text-gray-600">Current Balance</td>
                  <td className="py-2 text-right font-medium text-gray-900">${cardData.balance.toLocaleString()}</td>
                </tr>
                <tr>
                  <td className="py-2 text-gray-600">Credit Limit</td>
                  <td className="py-2 text-right font-medium text-gray-900">${cardData.creditLimit.toLocaleString()}</td>
                </tr>
                <tr>
                  <td className="py-2 text-gray-600">Available Credit</td>
                  <td className="py-2 text-right font-medium text-gray-900">${cardData.availableCredit.toLocaleString()}</td>
                </tr>
                <tr>
                  <td className="py-2 text-gray-600">Last Payment</td>
                  <td className="py-2 text-right font-medium text-gray-900">${cardData.lastPayment.toLocaleString()}</td>
                </tr>
                <tr>
                  <td className="py-2 text-gray-600">Next Payment Due</td>
                  <td className="py-2 text-right font-medium text-gray-900">{cardData.nextPaymentDue}</td>
                </tr>
              </tbody>
            </table>
          </div>
        </div>

        {/* Credit Utilization */}
        <div className="mt-4 pt-4 border-t border-gray-200">
          <div className="flex justify-between items-center mb-2">
            <span className="text-sm text-gray-600">Credit Utilization</span>
            <span className="text-sm font-medium text-gray-900">{utilizationPercentage.toFixed(1)}%</span>
          </div>
          <div className="w-full bg-gray-200 h-2">
            <div
              className={`h-2 ${
                utilizationPercentage > 80 ? 'bg-red-600' :
                utilizationPercentage > 50 ? 'bg-yellow-600' : 'bg-green-600'
              }`}
              style={{ width: `${Math.min(utilizationPercentage, 100)}%` }}
            ></div>
          </div>
        </div>

        {/* Action Buttons */}
        <div className="mt-4 pt-4 border-t border-gray-200">
          <div className="flex gap-2">
            <button className="bg-gray-100 border border-gray-300 px-4 py-2 text-sm text-gray-700">
              Make Payment
            </button>
            <button className="bg-gray-100 border border-gray-300 px-4 py-2 text-sm text-gray-700">
              View Statements
            </button>
            <button className="bg-gray-100 border border-gray-300 px-4 py-2 text-sm text-gray-700">
              Account Settings
            </button>
          </div>
        </div>
      </div>
    </div>
  );
};

export default CreditCardHeader;
