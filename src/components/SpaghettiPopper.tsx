// SpaghettiPopper.ts
import confetti from 'canvas-confetti';

// Fire multiple bursts from bottom to top (100% screen height)
export const fireSpaghetti = () => {
  const numBursts = 8;

  for (let i = 0; i < numBursts; i++) {
    const delay = i * 100; // staggered bursts
    const verticalOrigin = 1 - i / (numBursts - 1); // 1 to 0 (bottom to top)

    setTimeout(() => {
      confetti({
        particleCount: 50,
        angle: 90,
        spread: 140,
        origin: { y: verticalOrigin }, // spread vertically
        startVelocity: 45,
        colors: ['#f39c12', '#e74c3c', '#27ae60', '#8e44ad', '#f1c40f'],
        shapes: ['square'],
      });
    }, delay);
  }
};
