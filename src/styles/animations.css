/* Custom Animations for FinoVators Dashboard */

@keyframes blob {
  0% {
    transform: translate(0px, 0px) scale(1);
  }
  33% {
    transform: translate(30px, -50px) scale(1.1);
  }
  66% {
    transform: translate(-20px, 20px) scale(0.9);
  }
  100% {
    transform: translate(0px, 0px) scale(1);
  }
}

@keyframes float {
  0%, 100% {
    transform: translateY(0px);
    opacity: 0.7;
  }
  50% {
    transform: translateY(-20px);
    opacity: 1;
  }
}

@keyframes gradient {
  0%, 100% {
    background-size: 200% 200%;
    background-position: left center;
  }
  50% {
    background-size: 200% 200%;
    background-position: right center;
  }
}

@keyframes glow {
  0%, 100% {
    box-shadow: 0 0 20px rgba(59, 130, 246, 0.5);
  }
  50% {
    box-shadow: 0 0 40px rgba(59, 130, 246, 0.8), 0 0 60px rgba(59, 130, 246, 0.6);
  }
}

@keyframes pulse-glow {
  0%, 100% {
    box-shadow: 0 0 20px rgba(168, 85, 247, 0.4);
  }
  50% {
    box-shadow: 0 0 40px rgba(168, 85, 247, 0.8), 0 0 60px rgba(168, 85, 247, 0.6);
  }
}

@keyframes neon-flicker {
  0%, 100% {
    text-shadow: 
      0 0 5px currentColor,
      0 0 10px currentColor,
      0 0 15px currentColor,
      0 0 20px #00ffff,
      0 0 35px #00ffff,
      0 0 40px #00ffff;
  }
  50% {
    text-shadow: 
      0 0 2px currentColor,
      0 0 5px currentColor,
      0 0 8px currentColor,
      0 0 12px #00ffff,
      0 0 18px #00ffff,
      0 0 25px #00ffff;
  }
}

@keyframes card-hover {
  0% {
    transform: translateY(0) rotateX(0);
  }
  100% {
    transform: translateY(-10px) rotateX(5deg);
  }
}

@keyframes shimmer {
  0% {
    background-position: -200% center;
  }
  100% {
    background-position: 200% center;
  }
}

@keyframes bounce-in {
  0% {
    transform: scale(0.3) translateY(100px);
    opacity: 0;
  }
  50% {
    transform: scale(1.05) translateY(-10px);
    opacity: 0.8;
  }
  70% {
    transform: scale(0.9) translateY(0px);
    opacity: 0.9;
  }
  100% {
    transform: scale(1) translateY(0px);
    opacity: 1;
  }
}

@keyframes slide-in-left {
  0% {
    transform: translateX(-100px);
    opacity: 0;
  }
  100% {
    transform: translateX(0);
    opacity: 1;
  }
}

@keyframes slide-in-right {
  0% {
    transform: translateX(100px);
    opacity: 0;
  }
  100% {
    transform: translateX(0);
    opacity: 1;
  }
}

@keyframes fade-in-up {
  0% {
    transform: translateY(30px);
    opacity: 0;
  }
  100% {
    transform: translateY(0);
    opacity: 1;
  }
}

@keyframes rotate-glow {
  0% {
    transform: rotate(0deg);
    filter: hue-rotate(0deg);
  }
  100% {
    transform: rotate(360deg);
    filter: hue-rotate(360deg);
  }
}

@keyframes matrix-rain {
  0% {
    transform: translateY(-100vh);
    opacity: 0;
  }
  10% {
    opacity: 1;
  }
  90% {
    opacity: 1;
  }
  100% {
    transform: translateY(100vh);
    opacity: 0;
  }
}

/* Utility Classes */
.animate-blob {
  animation: blob 7s infinite;
}

.animate-float {
  animation: float 6s ease-in-out infinite;
}

.animate-gradient {
  background-size: 200% 200%;
  animation: gradient 3s ease infinite;
}

.animate-glow {
  animation: glow 2s ease-in-out infinite alternate;
}

.animate-pulse-glow {
  animation: pulse-glow 2s ease-in-out infinite alternate;
}

.animate-neon-flicker {
  animation: neon-flicker 1.5s ease-in-out infinite alternate;
}

.animate-card-hover {
  transition: all 0.3s ease;
}

.animate-card-hover:hover {
  animation: card-hover 0.3s ease forwards;
}

.animate-shimmer {
  background: linear-gradient(90deg, transparent, rgba(255,255,255,0.4), transparent);
  background-size: 200% 100%;
  animation: shimmer 2s infinite;
}

.animate-bounce-in {
  animation: bounce-in 0.8s ease-out;
}

.animate-slide-in-left {
  animation: slide-in-left 0.6s ease-out;
}

.animate-slide-in-right {
  animation: slide-in-right 0.6s ease-out;
}

.animate-fade-in-up {
  animation: fade-in-up 0.6s ease-out;
}

.animate-rotate-glow {
  animation: rotate-glow 3s linear infinite;
}

.animate-matrix-rain {
  animation: matrix-rain 3s linear infinite;
}

/* Animation Delays */
.animation-delay-2000 {
  animation-delay: 2s;
}

.animation-delay-4000 {
  animation-delay: 4s;
}

/* Glassmorphism Effects */
.glass {
  background: rgba(255, 255, 255, 0.1);
  backdrop-filter: blur(10px);
  border: 1px solid rgba(255, 255, 255, 0.2);
}

.glass-dark {
  background: rgba(0, 0, 0, 0.1);
  backdrop-filter: blur(10px);
  border: 1px solid rgba(255, 255, 255, 0.1);
}

/* Neon Glow Effects */
.neon-cyan {
  box-shadow: 
    0 0 5px #00ffff,
    0 0 10px #00ffff,
    0 0 15px #00ffff,
    0 0 20px #00ffff;
}

.neon-purple {
  box-shadow: 
    0 0 5px #8b5cf6,
    0 0 10px #8b5cf6,
    0 0 15px #8b5cf6,
    0 0 20px #8b5cf6;
}

.neon-pink {
  box-shadow: 
    0 0 5px #ec4899,
    0 0 10px #ec4899,
    0 0 15px #ec4899,
    0 0 20px #ec4899;
}

/* Holographic Effects */
.holographic {
  background: linear-gradient(45deg, #ff0080, #ff8c00, #40e0d0, #ff0080);
  background-size: 400% 400%;
  animation: gradient 3s ease infinite;
}

/* 3D Transform Effects */
.transform-3d {
  transform-style: preserve-3d;
  perspective: 1000px;
}

.card-3d {
  transition: transform 0.3s ease;
}

.card-3d:hover {
  transform: rotateY(10deg) rotateX(10deg) translateZ(20px);
}
