import axios from 'axios';

const apiClient = axios.create({
    baseURL: 'https://app-c5fb9e9d-d081-4145-b177-cbbf219e2053.cleverapps.io/', // Replace with your API base URL
    headers: {
        'Content-Type': 'application/json',
    },
});

// Example GET request
export const fetchData = async (endpoint: string) => {
    try {
        const response = await apiClient.get(endpoint);
        return response.data;
    } catch (error) {
        console.error('Error fetching data:', error);
        throw error;
    }
};

// Example POST request
export const postData = async (endpoint: string, data: any) => {
    try {
        const response = await apiClient.post(endpoint, data);
        return response.data;
    } catch (error) {
        console.error('Error posting data:', error);
        throw error;
    }
};