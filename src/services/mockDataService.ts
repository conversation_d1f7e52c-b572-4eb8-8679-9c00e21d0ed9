import { format, subMonths, addDays, startOfMonth, endOfMonth, getDate, getMonth, getYear } from 'date-fns';

export interface Transaction {
  id: number;
  merchant: string;
  amount: number;
  date: string;
  category: string;
  location: string;
  coordinates: { lat: number; lng: number };
  cashback: number;
  time: string;
}

export interface PaymentSummaryData {
  currentBalance: number;
  creditLimit: number;
  availableCredit: number;
  minimumPayment: number;
  dueDate: string;
  totalSpent: number;
  cashbackEarned: number;
  paymentStatus: string;
}

export interface BadgeData {
  id: number;
  name: string;
  description: string;
  earned: boolean;
  progress: number;
  category: string;
  reward: string;
  color: string;
}

export interface SuggestionData {
  id: number;
  type: string;
  priority: string;
  title: string;
  description: string;
  potentialSavings: number | null;
  category: string;
  color: string;
}

// Base data templates
const merchantTemplates = [
  { name: 'Starbucks Coffee', category: 'Coffee', location: 'Downtown Seattle', coords: { lat: 47.6062, lng: -122.3321 } },
  { name: 'Whole Foods Market', category: 'Groceries', location: 'Capitol Hill', coords: { lat: 47.6205, lng: -122.3212 } },
  { name: 'Shell Gas Station', category: 'Gas', location: 'I-5 Corridor', coords: { lat: 47.5952, lng: -122.3316 } },
  { name: 'Amazon Fresh', category: 'Groceries', location: 'Bellevue', coords: { lat: 47.6101, lng: -122.2015 } },
  { name: 'Target', category: 'Retail', location: 'Northgate', coords: { lat: 47.7031, lng: -122.3250 } },
  { name: 'Chipotle Mexican Grill', category: 'Dining', location: 'University District', coords: { lat: 47.6587, lng: -122.3123 } },
  { name: 'REI Co-op', category: 'Outdoor', location: 'SoDo', coords: { lat: 47.5868, lng: -122.3284 } },
  { name: 'Trader Joe\'s', category: 'Groceries', location: 'Queen Anne', coords: { lat: 47.6236, lng: -122.3564 } },
  { name: 'Costco Wholesale', category: 'Wholesale', location: 'Issaquah', coords: { lat: 47.5301, lng: -122.0326 } },
  { name: 'McDonald\'s', category: 'Fast Food', location: 'Pike Place', coords: { lat: 47.6085, lng: -122.3351 } },
  { name: 'Safeway', category: 'Groceries', location: 'Ballard', coords: { lat: 47.6684, lng: -122.3834 } },
  { name: 'Home Depot', category: 'Home Improvement', location: 'Renton', coords: { lat: 47.4829, lng: -122.2171 } },
  { name: 'Best Buy', category: 'Electronics', location: 'Redmond', coords: { lat: 47.6740, lng: -122.1215 } },
  { name: 'Panera Bread', category: 'Dining', location: 'Kirkland', coords: { lat: 47.6815, lng: -122.2087 } },
  { name: 'CVS Pharmacy', category: 'Pharmacy', location: 'Capitol Hill', coords: { lat: 47.6194, lng: -122.3209 } }
];

const categoryAmountRanges: Record<string, { min: number; max: number; cashbackRate: number }> = {
  'Coffee': { min: 3.50, max: 12.00, cashbackRate: 0.05 },
  'Groceries': { min: 25.00, max: 180.00, cashbackRate: 0.03 },
  'Gas': { min: 35.00, max: 85.00, cashbackRate: 0.04 },
  'Dining': { min: 15.00, max: 95.00, cashbackRate: 0.06 },
  'Retail': { min: 20.00, max: 250.00, cashbackRate: 0.02 },
  'Fast Food': { min: 8.00, max: 25.00, cashbackRate: 0.02 },
  'Electronics': { min: 50.00, max: 800.00, cashbackRate: 0.01 },
  'Pharmacy': { min: 10.00, max: 45.00, cashbackRate: 0.02 },
  'Home Improvement': { min: 30.00, max: 300.00, cashbackRate: 0.01 },
  'Wholesale': { min: 80.00, max: 350.00, cashbackRate: 0.02 },
  'Outdoor': { min: 40.00, max: 400.00, cashbackRate: 0.02 }
};

// Utility functions
const getRandomElement = <T>(array: T[]): T => array[Math.floor(Math.random() * array.length)];
const getRandomAmount = (min: number, max: number): number => Math.round((Math.random() * (max - min) + min) * 100) / 100;
const getRandomTime = (): string => {
  const hours = Math.floor(Math.random() * 12) + 1;
  const minutes = Math.floor(Math.random() * 60);
  const ampm = Math.random() > 0.5 ? 'AM' : 'PM';
  return `${hours}:${minutes.toString().padStart(2, '0')} ${ampm}`;
};

// Generate transactions for a specific month
export const generateTransactionsForMonth = (date: Date): Transaction[] => {
  const monthStart = startOfMonth(date);
  const monthEnd = endOfMonth(date);
  const transactions: Transaction[] = [];
  
  // Generate 15-35 transactions per month
  const transactionCount = Math.floor(Math.random() * 20) + 15;
  
  for (let i = 0; i < transactionCount; i++) {
    const merchant = getRandomElement(merchantTemplates);
    const range = categoryAmountRanges[merchant.category];
    const amount = getRandomAmount(range.min, range.max);
    const cashback = Math.round(amount * range.cashbackRate * 100) / 100;
    
    // Random date within the month
    const dayOffset = Math.floor(Math.random() * (monthEnd.getDate() - monthStart.getDate() + 1));
    const transactionDate = addDays(monthStart, dayOffset);
    
    transactions.push({
      id: i + 1,
      merchant: merchant.name,
      amount,
      date: format(transactionDate, 'yyyy-MM-dd'),
      category: merchant.category,
      location: merchant.location,
      coordinates: merchant.coords,
      cashback,
      time: getRandomTime()
    });
  }
  
  return transactions.sort((a, b) => new Date(b.date).getTime() - new Date(a.date).getTime());
};

// Generate payment summary for a specific month
export const generatePaymentSummaryForMonth = (date: Date, transactions: Transaction[]): PaymentSummaryData => {
  const totalSpent = transactions.reduce((sum, t) => sum + t.amount, 0);
  const cashbackEarned = transactions.reduce((sum, t) => sum + t.cashback, 0);
  
  // Base values that vary by month
  const monthOffset = getMonth(date);
  const baseBalance = 2500 + (monthOffset * 150) + (Math.random() * 500);
  const currentBalance = Math.round((baseBalance + totalSpent) * 100) / 100;
  
  const creditLimit = 10000;
  const availableCredit = creditLimit - currentBalance;
  const minimumPayment = Math.round(currentBalance * 0.03 * 100) / 100;
  
  // Due date is typically 15th of next month
  const nextMonth = addDays(endOfMonth(date), 15);
  
  return {
    currentBalance,
    creditLimit,
    availableCredit,
    minimumPayment,
    dueDate: format(nextMonth, 'MMMM dd, yyyy'),
    totalSpent: Math.round(totalSpent * 100) / 100,
    cashbackEarned: Math.round(cashbackEarned * 100) / 100,
    paymentStatus: currentBalance < creditLimit * 0.3 ? 'On Time' : 'High Utilization'
  };
};

// Generate badge progress for a specific month
export const generateBadgeProgressForMonth = (date: Date, transactions: Transaction[]): BadgeData[] => {
  const monthsFromNow = Math.abs(getMonth(new Date()) - getMonth(date)) + (getYear(new Date()) - getYear(date)) * 12;
  
  const coffeeSpent = transactions.filter(t => t.category === 'Coffee').reduce((sum, t) => sum + t.amount, 0);
  const retailCount = transactions.filter(t => t.category === 'Retail').length;
  const gasSpent = transactions.filter(t => t.category === 'Gas').reduce((sum, t) => sum + t.amount, 0);
  const diningCount = transactions.filter(t => t.category === 'Dining').length;
  
  return [
    {
      id: 1,
      name: 'Coffee Connoisseur',
      description: 'Spent $500+ on coffee shops',
      earned: coffeeSpent >= 500 || monthsFromNow > 2,
      progress: Math.min(100, Math.round((coffeeSpent / 500) * 100)),
      category: 'Dining',
      reward: '5% cashback on coffee',
      color: 'amber'
    },
    {
      id: 2,
      name: 'Shopping Star',
      description: 'Made 50+ retail purchases',
      earned: retailCount >= 50 || monthsFromNow > 3,
      progress: Math.min(100, Math.round((retailCount / 50) * 100)),
      category: 'Retail',
      reward: '3% cashback on retail',
      color: 'pink'
    },
    {
      id: 3,
      name: 'Gas Saver',
      description: 'Spent $1000+ on gas stations',
      earned: gasSpent >= 1000 || monthsFromNow > 4,
      progress: Math.min(100, Math.round((gasSpent / 1000) * 100)),
      category: 'Transportation',
      reward: '4% cashback on gas',
      color: 'blue'
    },
    {
      id: 4,
      name: 'Foodie Explorer',
      description: 'Dined at 25+ different restaurants',
      earned: diningCount >= 25 || monthsFromNow > 5,
      progress: Math.min(100, Math.round((diningCount / 25) * 100)),
      category: 'Dining',
      reward: '6% cashback on dining',
      color: 'orange'
    }
  ];
};

// Generate suggestions for a specific month
export const generateSuggestionsForMonth = (date: Date, paymentSummary: PaymentSummaryData): SuggestionData[] => {
  const utilizationRate = (paymentSummary.currentBalance / paymentSummary.creditLimit) * 100;
  const isCurrentMonth = getMonth(date) === getMonth(new Date()) && getYear(date) === getYear(new Date());
  
  const suggestions: SuggestionData[] = [];
  
  if (utilizationRate > 30) {
    suggestions.push({
      id: 1,
      type: 'warning',
      priority: 'high',
      title: 'High Credit Utilization',
      description: `Your utilization is at ${utilizationRate.toFixed(1)}%. Consider paying down your balance.`,
      potentialSavings: null,
      category: 'Credit Health',
      color: 'red'
    });
  }
  
  if (isCurrentMonth) {
    suggestions.push({
      id: 2,
      type: 'cashback',
      priority: 'high',
      title: 'Maximize Grocery Cashback',
      description: 'Switch to your Discover card for groceries this quarter to earn 5% instead of 1%',
      potentialSavings: 48.50,
      category: 'Cashback Optimization',
      color: 'green'
    });
  }
  
  suggestions.push({
    id: 3,
    type: 'goal',
    priority: 'medium',
    title: 'Monthly Cashback Goal',
    description: `You earned $${paymentSummary.cashbackEarned.toFixed(2)} in cashback this month.`,
    potentialSavings: null,
    category: 'Goal Tracking',
    color: 'purple'
  });
  
  return suggestions;
};

// Main function to get all data for a specific month
export const getDataForMonth = (date: Date) => {
  const transactions = generateTransactionsForMonth(date);
  const paymentSummary = generatePaymentSummaryForMonth(date, transactions);
  const badges = generateBadgeProgressForMonth(date, transactions);
  const suggestions = generateSuggestionsForMonth(date, paymentSummary);
  
  return {
    transactions,
    paymentSummary,
    badges,
    suggestions
  };
};
