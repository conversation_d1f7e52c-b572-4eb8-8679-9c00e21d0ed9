import { format, addDays, startOfMonth, endOf<PERSON>onth, getMonth, getYear } from 'date-fns';
import { Coffee, ShoppingBag, Car, Utensils, LucideIcon } from 'lucide-react';

export interface Transaction {
  id: number;
  merchant: string;
  amount: number;
  date: string;
  category: string;
  location: string;
  coordinates: { lat: number; lng: number };
  cashback: number;
  time: string;
}

export interface PaymentSummaryData {
  currentBalance: number;
  creditLimit: number;
  availableCredit: number;
  minimumPayment: number;
  dueDate: string;
  totalSpent: number;
  cashbackEarned: number;
  paymentStatus: string;
}

export interface BadgeData {
  id: number;
  name: string;
  description: string;
  icon: LucideIcon;
  earned: boolean;
  progress: number;
  category: string;
  reward: string;
  color: string;
  detailedDescription?: string;
  requirements?: string[];
  tips?: string[];
}

export interface SuggestionData {
  id: number;
  type: string;
  priority: string;
  title: string;
  description: string;
  potentialSavings: number | null;
  category: string;
  color: string;
}

// Base data templates
const merchantTemplates = [
  { name: 'Cafe Coffee Day', category: 'Coffee', location: 'Connaught Place, Delhi', coords: { lat: 28.6315, lng: 77.2167 } },
  { name: 'Big Bazaar', category: 'Groceries', location: 'Andheri, Mumbai', coords: { lat: 19.1136, lng: 72.8697 } },
  { name: 'Indian Oil Petrol Pump', category: 'Gas', location: 'Koramangala, Bangalore', coords: { lat: 12.9352, lng: 77.6245 } },
  { name: 'Spencer\'s Retail', category: 'Groceries', location: 'Park Street, Kolkata', coords: { lat: 22.5726, lng: 88.3639 } },
  { name: 'Reliance Digital', category: 'Electronics', location: 'Cyber City, Gurgaon', coords: { lat: 28.4595, lng: 77.0266 } },
  { name: 'Domino\'s Pizza', category: 'Dining', location: 'MG Road, Pune', coords: { lat: 18.5204, lng: 73.8567 } },
  { name: 'Croma Electronics', category: 'Electronics', location: 'Indiranagar, Bangalore', coords: { lat: 12.9716, lng: 77.6412 } },
  { name: 'More Supermarket', category: 'Groceries', location: 'Sector 18, Noida', coords: { lat: 28.5355, lng: 77.3910 } },
  { name: 'D-Mart', category: 'Wholesale', location: 'Thane, Mumbai', coords: { lat: 19.2183, lng: 72.9781 } },
  { name: 'McDonald\'s', category: 'Fast Food', location: 'Khan Market, Delhi', coords: { lat: 28.5984, lng: 77.2319 } },
  { name: 'Lifestyle Stores', category: 'Retail', location: 'Phoenix Mall, Chennai', coords: { lat: 13.0827, lng: 80.2707 } },
  { name: 'Home Centre', category: 'Home Improvement', location: 'Whitefield, Bangalore', coords: { lat: 12.9698, lng: 77.7500 } },
  { name: 'Croma', category: 'Electronics', location: 'Bandra, Mumbai', coords: { lat: 19.0596, lng: 72.8295 } },
  { name: 'Haldiram\'s', category: 'Dining', location: 'CP, Delhi', coords: { lat: 28.6315, lng: 77.2167 } },
  { name: 'Apollo Pharmacy', category: 'Pharmacy', location: 'Jubilee Hills, Hyderabad', coords: { lat: 17.4399, lng: 78.4983 } },
  { name: 'IndiGo Airlines', category: 'Travel', location: 'IGI Airport, Delhi', coords: { lat: 28.5562, lng: 77.1000 } },
  { name: 'Ola Cabs', category: 'Transportation', location: 'Koramangala, Bangalore', coords: { lat: 12.9352, lng: 77.6245 } },
  { name: 'The Taj Hotel', category: 'Travel', location: 'Colaba, Mumbai', coords: { lat: 18.9220, lng: 72.8347 } },
  { name: 'MakeMyTrip', category: 'Travel', location: 'Online', coords: { lat: 28.6315, lng: 77.2167 } },
  { name: 'Uber', category: 'Transportation', location: 'Sector 5, Gurgaon', coords: { lat: 28.4595, lng: 77.0266 } }
];

const categoryAmountRanges: Record<string, { min: number; max: number; cashbackRate: number }> = {
  'Coffee': { min: 290, max: 1000, cashbackRate: 0.05 },
  'Groceries': { min: 2080, max: 14950, cashbackRate: 0.03 },
  'Gas': { min: 2910, max: 7070, cashbackRate: 0.04 },
  'Dining': { min: 1250, max: 7900, cashbackRate: 0.06 },
  'Retail': { min: 1660, max: 20800, cashbackRate: 0.02 },
  'Fast Food': { min: 665, max: 2080, cashbackRate: 0.02 },
  'Electronics': { min: 4160, max: 66500, cashbackRate: 0.01 },
  'Pharmacy': { min: 830, max: 3740, cashbackRate: 0.02 },
  'Home Improvement': { min: 2500, max: 24950, cashbackRate: 0.01 },
  'Wholesale': { min: 6650, max: 29100, cashbackRate: 0.02 },
  'Outdoor': { min: 3330, max: 33250, cashbackRate: 0.02 },
  'Travel': { min: 12500, max: 208000, cashbackRate: 0.03 },
  'Transportation': { min: 665, max: 3740, cashbackRate: 0.02 }
};

// Utility functions
const getRandomElement = <T>(array: T[]): T => array[Math.floor(Math.random() * array.length)];
const getRandomAmount = (min: number, max: number): number => Math.round((Math.random() * (max - min) + min) * 100) / 100;
const getRandomTime = (): string => {
  const hours = Math.floor(Math.random() * 12) + 1;
  const minutes = Math.floor(Math.random() * 60);
  const ampm = Math.random() > 0.5 ? 'AM' : 'PM';
  return `${hours}:${minutes.toString().padStart(2, '0')} ${ampm}`;
};

// Generate transactions for a specific month
export const generateTransactionsForMonth = (date: Date): Transaction[] => {
  const monthStart = startOfMonth(date);
  const monthEnd = endOfMonth(date);
  const transactions: Transaction[] = [];
  
  // Generate 15-35 transactions per month
  const transactionCount = Math.floor(Math.random() * 20) + 15;
  
  for (let i = 0; i < transactionCount; i++) {
    const merchant = getRandomElement(merchantTemplates);
    const range = categoryAmountRanges[merchant.category];
    const amount = getRandomAmount(range.min, range.max);
    const cashback = Math.round(amount * range.cashbackRate * 100) / 100;
    
    // Random date within the month
    const dayOffset = Math.floor(Math.random() * (monthEnd.getDate() - monthStart.getDate() + 1));
    const transactionDate = addDays(monthStart, dayOffset);
    
    transactions.push({
      id: i + 1,
      merchant: merchant.name,
      amount,
      date: format(transactionDate, 'yyyy-MM-dd'),
      category: merchant.category,
      location: merchant.location,
      coordinates: merchant.coords,
      cashback,
      time: getRandomTime()
    });
  }
  
  return transactions.sort((a, b) => new Date(b.date).getTime() - new Date(a.date).getTime());
};

// Generate payment summary for a specific month
export const generatePaymentSummaryForMonth = (date: Date, transactions: Transaction[]): PaymentSummaryData => {
  const totalSpent = transactions.reduce((sum, t) => sum + t.amount, 0);
  const cashbackEarned = transactions.reduce((sum, t) => sum + t.cashback, 0);

  // Base values that vary by month (converted to INR)
  const monthOffset = getMonth(date);
  const baseBalance = 208000 + (monthOffset * 12500) + (Math.random() * 41500); // ~₹2,08,000 base
  const currentBalance = Math.round((baseBalance + totalSpent) * 100) / 100;

  const creditLimit = 832000; // ₹8,32,000 (equivalent to $10,000)
  const availableCredit = creditLimit - currentBalance;
  const minimumPayment = Math.round(currentBalance * 0.03 * 100) / 100;
  
  // Due date is typically 15th of next month
  const nextMonth = addDays(endOfMonth(date), 15);
  
  return {
    currentBalance,
    creditLimit,
    availableCredit,
    minimumPayment,
    dueDate: format(nextMonth, 'MMMM dd, yyyy'),
    totalSpent: Math.round(totalSpent * 100) / 100,
    cashbackEarned: Math.round(cashbackEarned * 100) / 100,
    paymentStatus: currentBalance < creditLimit * 0.3 ? 'On Time' : 'High Utilization'
  };
};

// Generate badge progress for a specific month
export const generateBadgeProgressForMonth = (date: Date, transactions: Transaction[]): BadgeData[] => {
  const monthsFromNow = Math.abs(getMonth(new Date()) - getMonth(date)) + (getYear(new Date()) - getYear(date)) * 12;
  
  const coffeeSpent = transactions.filter(t => t.category === 'Coffee').reduce((sum, t) => sum + t.amount, 0);
  const retailCount = transactions.filter(t => t.category === 'Retail').length;
  const gasSpent = transactions.filter(t => t.category === 'Gas').reduce((sum, t) => sum + t.amount, 0);
  const diningCount = transactions.filter(t => t.category === 'Dining').length;
  
  return [
    {
      id: 1,
      name: 'Coffee Connoisseur',
      description: 'Spent $500+ on coffee shops',
      icon: Coffee,
      earned: coffeeSpent >= 500 || monthsFromNow > 2,
      progress: Math.min(100, Math.round((coffeeSpent / 500) * 100)),
      category: 'Dining',
      reward: '5% cashback on coffee',
      color: 'amber',
      detailedDescription: 'You\'ve mastered the art of coffee spending! This badge recognizes your dedication to quality caffeine consumption.',
      requirements: ['Spend $500+ at coffee shops', 'Visit at least 5 different coffee shops'],
      tips: ['Look for coffee shops with bonus cashback offers', 'Consider getting a coffee subscription for regular rewards']
    },
    {
      id: 2,
      name: 'Shopping Star',
      description: 'Made 50+ retail purchases',
      icon: ShoppingBag,
      earned: retailCount >= 50 || monthsFromNow > 3,
      progress: Math.min(100, Math.round((retailCount / 50) * 100)),
      category: 'Retail',
      reward: '3% cashback on retail',
      color: 'pink',
      detailedDescription: 'You\'re a retail champion! This badge celebrates your consistent shopping habits and smart purchasing decisions.',
      requirements: ['Complete 50+ retail transactions', 'Shop at least 10 different stores'],
      tips: ['Time your purchases during sales events', 'Use cashback apps for additional rewards']
    },
    {
      id: 3,
      name: 'Gas Saver',
      description: 'Spent $1000+ on gas stations',
      icon: Car,
      earned: gasSpent >= 1000 || monthsFromNow > 4,
      progress: Math.min(100, Math.round((gasSpent / 1000) * 100)),
      category: 'Transportation',
      reward: '4% cashback on gas',
      color: 'blue',
      detailedDescription: 'Almost there! This badge rewards consistent fuel purchases and smart transportation spending.',
      requirements: ['Spend $1000+ at gas stations', 'Use the same gas station chain for consistency'],
      tips: ['Look for gas stations with membership programs', 'Fill up during off-peak hours for better deals']
    },
    {
      id: 4,
      name: 'Foodie Explorer',
      description: 'Dined at 25+ different restaurants',
      icon: Utensils,
      earned: diningCount >= 25 || monthsFromNow > 5,
      progress: Math.min(100, Math.round((diningCount / 25) * 100)),
      category: 'Dining',
      reward: '6% cashback on dining',
      color: 'orange',
      detailedDescription: 'You\'re on your way to becoming a dining expert! This badge celebrates culinary diversity and restaurant exploration.',
      requirements: ['Dine at 25+ different restaurants', 'Try at least 5 different cuisine types'],
      tips: ['Explore new neighborhoods for dining', 'Check for restaurant week specials', 'Use dining reward apps']
    }
  ];
};

// Generate suggestions for a specific month
export const generateSuggestionsForMonth = (date: Date, paymentSummary: PaymentSummaryData): SuggestionData[] => {
  const utilizationRate = (paymentSummary.currentBalance / paymentSummary.creditLimit) * 100;
  const isCurrentMonth = getMonth(date) === getMonth(new Date()) && getYear(date) === getYear(new Date());
  
  const suggestions: SuggestionData[] = [];
  
  if (utilizationRate > 30) {
    suggestions.push({
      id: 1,
      type: 'warning',
      priority: 'high',
      title: 'High Credit Utilization',
      description: `Your utilization is at ${utilizationRate.toFixed(1)}%. Consider paying down your balance.`,
      potentialSavings: null,
      category: 'Credit Health',
      color: 'red'
    });
  }
  
  if (isCurrentMonth) {
    suggestions.push({
      id: 2,
      type: 'cashback',
      priority: 'high',
      title: 'Maximize Grocery Cashback',
      description: 'Switch to your Discover card for groceries this quarter to earn 5% instead of 1%',
      potentialSavings: 48.50,
      category: 'Cashback Optimization',
      color: 'green'
    });
  }
  
  suggestions.push({
    id: 3,
    type: 'goal',
    priority: 'medium',
    title: 'Monthly Cashback Goal',
    description: `You earned ₹${paymentSummary.cashbackEarned.toLocaleString('en-IN')} in cashback this month.`,
    potentialSavings: null,
    category: 'Goal Tracking',
    color: 'purple'
  });
  
  return suggestions;
};

// Main function to get all data for a specific month
export const getDataForMonth = (date: Date) => {
  const transactions = generateTransactionsForMonth(date);
  const paymentSummary = generatePaymentSummaryForMonth(date, transactions);
  const badges = generateBadgeProgressForMonth(date, transactions);
  const suggestions = generateSuggestionsForMonth(date, paymentSummary);
  
  return {
    transactions,
    paymentSummary,
    badges,
    suggestions
  };
};
