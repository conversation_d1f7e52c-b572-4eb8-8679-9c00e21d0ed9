
import React, { useState, useEffect } from 'react';
import { useDateContext } from '../contexts/DateContext';
// import CalendarSelector from '../components/CalendarSelector';
import CreditCardHeader from '../components/CreditCardHeader';
import PaymentSummary from '../components/PaymentSummary';
import BadgesSection from '../components/BadgesSection';
import SmartSuggestions from '../components/SmartSuggestions';
import TransactionMap from '../components/TransactionMap';
import SmartCashbackOptimizer from '../components/SmartCashbackOptimizer';
import { Loader2, Bell, Settings, User, Search, Menu, Sparkles, TrendingUp, Zap, CalendarDays, Calendar } from 'lucide-react';
import { format } from 'date-fns';

// Simple CalendarSelector
const CalendarSelector = ({ selectedDate, onDateChange }: { selectedDate: Date; onDateChange: (date: Date) => void }) => {
  return (
    <div className="bg-white border border-gray-300">
      <div className="px-4 py-3 border-b border-gray-300">
        <div className="flex items-center justify-between">
          <div>
            <h3 className="text-sm font-medium text-gray-900">Reporting Period</h3>
          </div>
          <div className="flex items-center gap-4">
            <span className="text-sm text-gray-900">{format(selectedDate, 'MMMM yyyy')}</span>
            <button className="bg-gray-100 border border-gray-300 px-3 py-1 text-sm text-gray-700">
              Change Period
            </button>
          </div>
        </div>
      </div>
    </div>
  );
};

const Index = () => {
  const { selectedDate, setSelectedDate, isLoading } = useDateContext();
  const [mounted, setMounted] = useState(false);

  useEffect(() => {
    setMounted(true);
  }, []);

  return (
    <div className="min-h-screen bg-white">
      {/* Clean Professional Background */}
      <div className="absolute inset-0 bg-white"></div>

      {/* Simple Professional Navigation */}
      <nav className="bg-white border-b border-gray-300">
        <div className="max-w-7xl mx-auto px-4">
          <div className="flex justify-between items-center h-14">
            {/* Simple Logo */}
            <div className="flex items-center">
              <h1 className="text-lg font-medium text-gray-900">FinoVators</h1>
            </div>

            {/* Simple Navigation */}
            <div className="flex items-center space-x-6">
              <a href="#" className="text-sm text-gray-900 font-medium">Dashboard</a>
              <a href="#" className="text-sm text-gray-600">Transactions</a>
              <a href="#" className="text-sm text-gray-600">Reports</a>
              <a href="#" className="text-sm text-gray-600">Settings</a>
            </div>

            {/* Simple User Info */}
            <div className="flex items-center gap-4">
              <span className="text-sm text-gray-600">John Smith</span>
              <button className="text-sm text-gray-600">Logout</button>
            </div>
          </div>
        </div>
      </nav>

      {/* Main Content */}
      <div className="max-w-7xl mx-auto px-4 py-6">
        {/* Simple Page Header */}
        <div className="mb-6">
          <h1 className="text-xl font-medium text-gray-900 mb-1">Account Dashboard</h1>
          <p className="text-sm text-gray-600">Overview of your financial accounts</p>
        </div>

        {/* Floating Calendar Selector */}
        <div className={`mb-12 transition-all duration-1000 delay-900 ${mounted ? 'translate-y-0 opacity-100' : 'translate-y-8 opacity-0'}`}>
          <CalendarSelector
            selectedDate={selectedDate}
            onDateChange={setSelectedDate}
          />
        </div>

        {/* Credit Card Overview */}
        <div className="mb-6">
          <CreditCardHeader className="w-full" />
        </div>

        {/* Simple Dashboard Grid */}
        <div className="grid grid-cols-1 lg:grid-cols-3 gap-6">
          {/* Left Column */}
          <div className="lg:col-span-2 space-y-6">
            <PaymentSummary selectedDate={selectedDate} />
            <TransactionMap selectedDate={selectedDate} />
          </div>

          {/* Right Column */}
          <div className="space-y-6">
            <SmartSuggestions selectedDate={selectedDate} />
            <BadgesSection selectedDate={selectedDate} />
            <SmartCashbackOptimizer selectedDate={selectedDate} />
          </div>
        </div>
      </div>
    </div>
  );
};

export default Index;
            <div className={`group transition-all duration-1000 delay-1500 ${mounted ? 'translate-y-0 opacity-100' : 'translate-y-8 opacity-0'}`}>
              <div className="bg-white/10 backdrop-blur-xl rounded-3xl shadow-2xl border border-white/20 hover:bg-white/20 transition-all duration-500 transform hover:scale-[1.02] hover:shadow-purple-500/25">
                <div className="px-8 py-6 border-b border-white/20">
                  <div className="flex items-center gap-3">
                    <div className="w-12 h-12 bg-gradient-to-r from-purple-400 to-pink-500 rounded-2xl flex items-center justify-center shadow-lg group-hover:animate-pulse">
                      <Search className="h-6 w-6 text-white" />
                    </div>
                    <div>
                      <h2 className="text-2xl font-bold text-white">Transaction Map</h2>
                      <p className="text-white/70">Interactive spending visualization</p>
                    </div>
                  </div>
                </div>
                <div className="p-8">
                  <TransactionMap />
                </div>
              </div>
            </div>

            {/* Cashback Optimizer - Holographic */}
            <div className={`group transition-all duration-1000 delay-1700 ${mounted ? 'translate-y-0 opacity-100' : 'translate-y-8 opacity-0'}`}>
              <div className="bg-white/10 backdrop-blur-xl rounded-3xl shadow-2xl border border-white/20 hover:bg-white/20 transition-all duration-500 transform hover:scale-[1.02] hover:shadow-green-500/25">
                <div className="px-8 py-6 border-b border-white/20">
                  <div className="flex items-center gap-3">
                    <div className="w-12 h-12 bg-gradient-to-r from-green-400 to-emerald-500 rounded-2xl flex items-center justify-center shadow-lg group-hover:animate-pulse">
                      <Zap className="h-6 w-6 text-white" />
                    </div>
                    <div>
                      <h2 className="text-2xl font-bold text-white">Cashback Optimizer</h2>
                      <p className="text-white/70">AI-powered reward maximization</p>
                    </div>
                  </div>
                </div>
                <div className="p-8">
                  <SmartCashbackOptimizer />
                </div>
              </div>
            </div>
          </div>

          {/* Right Sidebar - Floating Cards */}
          <div className="lg:col-span-4 space-y-8">
            {/* Smart Suggestions - Cyberpunk */}
            <div className={`group transition-all duration-1000 delay-1400 ${mounted ? 'translate-y-0 opacity-100' : 'translate-y-8 opacity-0'}`}>
              <div className="bg-white/10 backdrop-blur-xl rounded-3xl shadow-2xl border border-white/20 hover:bg-white/20 transition-all duration-500 transform hover:scale-[1.02] hover:shadow-yellow-500/25">
                <div className="px-6 py-5 border-b border-white/20">
                  <div className="flex items-center gap-3">
                    <div className="w-10 h-10 bg-gradient-to-r from-yellow-400 to-orange-500 rounded-xl flex items-center justify-center shadow-lg group-hover:animate-pulse">
                      <Sparkles className="h-5 w-5 text-white" />
                    </div>
                    <div>
                      <h2 className="text-xl font-bold text-white">Smart Insights</h2>
                      <p className="text-white/70 text-sm">AI recommendations</p>
                    </div>
                  </div>
                </div>
                <div className="p-6">
                  <SmartSuggestions />
                </div>
              </div>
            </div>

            {/* Achievement Badges - Neon */}
            <div className={`group transition-all duration-1000 delay-1600 ${mounted ? 'translate-y-0 opacity-100' : 'translate-y-8 opacity-0'}`}>
              <div className="bg-white/10 backdrop-blur-xl rounded-3xl shadow-2xl border border-white/20 hover:bg-white/20 transition-all duration-500 transform hover:scale-[1.02] hover:shadow-pink-500/25">
                <div className="px-6 py-5 border-b border-white/20">
                  <div className="flex items-center gap-3">
                    <div className="w-10 h-10 bg-gradient-to-r from-pink-400 to-rose-500 rounded-xl flex items-center justify-center shadow-lg group-hover:animate-pulse">
                      <Sparkles className="h-5 w-5 text-white" />
                    </div>
                    <div>
                      <h2 className="text-xl font-bold text-white">Achievements</h2>
                      <p className="text-white/70 text-sm">Progress & milestones</p>
                    </div>
                  </div>
                </div>
                <div className="p-6">
                  <BadgesSection />
                </div>
              </div>
            </div>
          </div>
        </div>

        {/* Futuristic Footer */}
        <footer className={`mt-16 transition-all duration-1000 delay-2000 ${mounted ? 'translate-y-0 opacity-100' : 'translate-y-8 opacity-0'}`}>
          <div className="relative group">
            <div className="absolute -inset-1 bg-gradient-to-r from-cyan-400 via-purple-500 to-pink-500 rounded-2xl blur opacity-25 group-hover:opacity-75 transition duration-300 animate-gradient"></div>
            <div className="relative bg-white/10 backdrop-blur-xl rounded-2xl border border-white/20 p-6">
              <div className="flex items-center justify-center gap-4 text-white/70">
                <div className="flex items-center gap-2">
                  <div className="w-2 h-2 bg-green-400 rounded-full animate-pulse"></div>
                  <span className="text-sm">Neural Network Active</span>
                </div>
                <div className="w-px h-4 bg-white/20"></div>
                <div className="flex items-center gap-2">
                  <div className="w-2 h-2 bg-blue-400 rounded-full animate-pulse animation-delay-2000"></div>
                  <span className="text-sm">Quantum Processing</span>
                </div>
                <div className="w-px h-4 bg-white/20"></div>
                <div className="flex items-center gap-2">
                  <div className="w-2 h-2 bg-purple-400 rounded-full animate-pulse animation-delay-4000"></div>
                  <span className="text-sm">AI Optimization</span>
                </div>
              </div>
              <div className="text-center mt-4">
                <p className="text-white/60 text-xs">
                  © 2024 FinoVators • Powered by Advanced AI • Secured by Quantum Encryption
                </p>
              </div>
            </div>
          </div>
        </footer>
      </div>
    </div>
  );
};

export default Index;
