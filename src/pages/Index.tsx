
import React, { useState, useEffect } from 'react';
import { useDateContext } from '../contexts/DateContext';
// import CalendarSelector from '../components/CalendarSelector';
import CreditCardHeader from '../components/CreditCardHeader';
import PaymentSummary from '../components/PaymentSummary';
import BadgesSection from '../components/BadgesSection';
import SmartSuggestions from '../components/SmartSuggestions';
import TransactionMap from '../components/TransactionMap';
import SmartCashbackOptimizer from '../components/SmartCashbackOptimizer';
import { Loader2, Bell, Settings, User, Search, Menu, Sparkles, TrendingUp, Zap, CalendarDays, Calendar } from 'lucide-react';
import { format } from 'date-fns';

// Professional inline CalendarSelector
const CalendarSelector = ({ selectedDate, onDateChange }: { selectedDate: Date; onDateChange: (date: Date) => void }) => {
  return (
    <div className="bg-white rounded-lg shadow-sm border border-gray-200">
      <div className="px-6 py-4 border-b border-gray-100">
        <div className="flex items-center justify-between">
          <div className="flex items-center gap-3">
            <div className="w-8 h-8 bg-blue-600 rounded-lg flex items-center justify-center">
              <CalendarDays className="h-4 w-4 text-white" />
            </div>
            <div>
              <h3 className="text-base font-semibold text-gray-900">Reporting Period</h3>
              <p className="text-gray-600 text-sm">Select month to view data</p>
            </div>
          </div>

          <div className="flex items-center gap-4">
            <div className="text-right">
              <div className="text-lg font-semibold text-gray-900">
                {format(selectedDate, 'MMMM yyyy')}
              </div>
              <div className="text-gray-500 text-sm">Current selection</div>
            </div>
            <button className="bg-blue-600 hover:bg-blue-700 text-white px-4 py-2 rounded-lg text-sm font-medium transition-colors flex items-center gap-2">
              <Calendar className="h-4 w-4" />
              Change
            </button>
          </div>
        </div>
      </div>
    </div>
  );
};

const Index = () => {
  const { selectedDate, setSelectedDate, isLoading } = useDateContext();
  const [mounted, setMounted] = useState(false);

  useEffect(() => {
    setMounted(true);
  }, []);

  return (
    <div className="min-h-screen bg-gray-50">
      {/* Professional Background */}
      <div className="absolute inset-0 bg-gray-50">
        {/* Subtle grid pattern */}
        <div className="absolute inset-0 opacity-[0.02]">
          <div className="absolute top-0 left-0 w-full h-full" style={{
            backgroundImage: `linear-gradient(rgba(0,0,0,0.1) 1px, transparent 1px),
                             linear-gradient(90deg, rgba(0,0,0,0.1) 1px, transparent 1px)`,
            backgroundSize: '20px 20px'
          }}></div>
        </div>
      </div>

      {/* Professional Navigation */}
      <nav className="relative z-50 bg-white border-b border-gray-200 sticky top-0 shadow-sm">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="flex justify-between items-center h-16">
            {/* Logo and Brand */}
            <div className="flex items-center gap-3">
              <div className="w-8 h-8 bg-blue-600 rounded-lg flex items-center justify-center">
                <TrendingUp className="h-5 w-5 text-white" />
              </div>
              <div>
                <h1 className="text-lg font-semibold text-gray-900">FinoVators</h1>
                <p className="text-xs text-gray-500 hidden sm:block">Financial Management Platform</p>
              </div>
            </div>

            {/* Navigation Links */}
            <div className="hidden md:flex items-center space-x-8">
              <a href="#" className="text-sm font-medium text-blue-600 border-b-2 border-blue-600 pb-4">Dashboard</a>
              <a href="#" className="text-sm font-medium text-gray-500 hover:text-gray-900 transition-colors">Transactions</a>
              <a href="#" className="text-sm font-medium text-gray-500 hover:text-gray-900 transition-colors">Analytics</a>
              <a href="#" className="text-sm font-medium text-gray-500 hover:text-gray-900 transition-colors">Cards</a>
              <a href="#" className="text-sm font-medium text-gray-500 hover:text-gray-900 transition-colors">Settings</a>
            </div>

            {/* Professional Search Bar */}
            <div className="flex-1 max-w-md mx-8">
              <div className="relative">
                <div className="flex items-center bg-gray-50 rounded-lg border border-gray-200 px-3 py-2 focus-within:ring-2 focus-within:ring-blue-500 focus-within:border-blue-500 transition-all">
                  <Search className="h-4 w-4 text-gray-400 mr-2" />
                  <input
                    type="text"
                    placeholder="Search transactions..."
                    className="flex-1 bg-transparent text-gray-900 placeholder-gray-500 text-sm focus:outline-none"
                  />
                </div>
              </div>
            </div>

            {/* Professional Action Buttons */}
            <div className="flex items-center gap-2">
              <button className="relative p-2 text-gray-400 hover:text-gray-600 transition-colors">
                <Bell className="h-5 w-5" />
                <div className="absolute -top-1 -right-1 w-2 h-2 bg-red-500 rounded-full"></div>
              </button>
              <button className="p-2 text-gray-400 hover:text-gray-600 transition-colors">
                <Settings className="h-5 w-5" />
              </button>
              <div className="flex items-center gap-2 ml-4 pl-4 border-l border-gray-200">
                <div className="w-8 h-8 bg-gray-200 rounded-full flex items-center justify-center">
                  <User className="h-4 w-4 text-gray-600" />
                </div>
                <div className="hidden sm:block">
                  <p className="text-sm font-medium text-gray-900">John Smith</p>
                  <p className="text-xs text-gray-500">Premium Member</p>
                </div>
              </div>
            </div>
          </div>
        </div>
      </nav>

      {/* Main Content */}
      <div className="relative z-10 max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
        {/* Professional Page Header */}
        <div className="mb-8">
          <div className="flex items-center justify-between">
            <div>
              <h1 className="text-2xl font-bold text-gray-900 mb-1">Dashboard Overview</h1>
              <p className="text-gray-600">Comprehensive view of your financial portfolio</p>
            </div>
            <div className="flex items-center gap-4">
              <div className="flex items-center gap-2 text-sm text-gray-500">
                <div className="w-2 h-2 bg-green-500 rounded-full"></div>
                <span>Live data</span>
              </div>
              <button className="bg-blue-600 hover:bg-blue-700 text-white px-4 py-2 rounded-lg text-sm font-medium transition-colors">
                Export Report
              </button>
            </div>
          </div>
        </div>

        {/* Floating Calendar Selector */}
        <div className={`mb-12 transition-all duration-1000 delay-900 ${mounted ? 'translate-y-0 opacity-100' : 'translate-y-8 opacity-0'}`}>
          <CalendarSelector
            selectedDate={selectedDate}
            onDateChange={setSelectedDate}
          />
        </div>

        {/* Futuristic Loading Overlay */}
        {isLoading && (
          <div className="fixed inset-0 bg-black/60 backdrop-blur-xl z-50 flex items-center justify-center">
            <div className="bg-white/10 backdrop-blur-xl rounded-3xl p-8 shadow-2xl border border-white/20 flex items-center gap-4">
              <div className="relative">
                <Loader2 className="h-8 w-8 animate-spin text-cyan-400" />
                <div className="absolute inset-0 h-8 w-8 border-2 border-purple-400/30 rounded-full animate-ping"></div>
              </div>
              <div>
                <span className="text-lg font-semibold text-white">Loading Financial Data</span>
                <div className="flex gap-1 mt-2">
                  <div className="w-2 h-2 bg-cyan-400 rounded-full animate-bounce"></div>
                  <div className="w-2 h-2 bg-purple-400 rounded-full animate-bounce" style={{animationDelay: '0.1s'}}></div>
                  <div className="w-2 h-2 bg-pink-400 rounded-full animate-bounce" style={{animationDelay: '0.2s'}}></div>
                </div>
              </div>
            </div>
          </div>
        )}

        {/* Credit Card Overview */}
        <div className="mb-8">
          <CreditCardHeader className="w-full" />
        </div>

        {/* Animated Dashboard Grid */}
        <div className="grid grid-cols-1 lg:grid-cols-12 gap-8">
          {/* Left Column - Main Content */}
          <div className="lg:col-span-8 space-y-8">
            {/* Payment Overview - Glassmorphism */}
            <div className={`group transition-all duration-1000 delay-1300 ${mounted ? 'translate-y-0 opacity-100' : 'translate-y-8 opacity-0'}`}>
              <div className="bg-white/10 backdrop-blur-xl rounded-3xl shadow-2xl border border-white/20 hover:bg-white/20 transition-all duration-500 transform hover:scale-[1.02] hover:shadow-cyan-500/25">
                <div className="px-8 py-6 border-b border-white/20">
                  <div className="flex items-center gap-3">
                    <div className="w-12 h-12 bg-gradient-to-r from-cyan-400 to-blue-500 rounded-2xl flex items-center justify-center shadow-lg group-hover:animate-pulse">
                      <TrendingUp className="h-6 w-6 text-white" />
                    </div>
                    <div>
                      <h2 className="text-2xl font-bold text-white">Payment Overview</h2>
                      <p className="text-white/70">Your financial snapshot with AI insights</p>
                    </div>
                  </div>
                </div>
                <div className="p-8">
                  <PaymentSummary />
                </div>
              </div>
            </div>

            {/* Transaction Map - Neon Glow */}
            <div className={`group transition-all duration-1000 delay-1500 ${mounted ? 'translate-y-0 opacity-100' : 'translate-y-8 opacity-0'}`}>
              <div className="bg-white/10 backdrop-blur-xl rounded-3xl shadow-2xl border border-white/20 hover:bg-white/20 transition-all duration-500 transform hover:scale-[1.02] hover:shadow-purple-500/25">
                <div className="px-8 py-6 border-b border-white/20">
                  <div className="flex items-center gap-3">
                    <div className="w-12 h-12 bg-gradient-to-r from-purple-400 to-pink-500 rounded-2xl flex items-center justify-center shadow-lg group-hover:animate-pulse">
                      <Search className="h-6 w-6 text-white" />
                    </div>
                    <div>
                      <h2 className="text-2xl font-bold text-white">Transaction Map</h2>
                      <p className="text-white/70">Interactive spending visualization</p>
                    </div>
                  </div>
                </div>
                <div className="p-8">
                  <TransactionMap />
                </div>
              </div>
            </div>

            {/* Cashback Optimizer - Holographic */}
            <div className={`group transition-all duration-1000 delay-1700 ${mounted ? 'translate-y-0 opacity-100' : 'translate-y-8 opacity-0'}`}>
              <div className="bg-white/10 backdrop-blur-xl rounded-3xl shadow-2xl border border-white/20 hover:bg-white/20 transition-all duration-500 transform hover:scale-[1.02] hover:shadow-green-500/25">
                <div className="px-8 py-6 border-b border-white/20">
                  <div className="flex items-center gap-3">
                    <div className="w-12 h-12 bg-gradient-to-r from-green-400 to-emerald-500 rounded-2xl flex items-center justify-center shadow-lg group-hover:animate-pulse">
                      <Zap className="h-6 w-6 text-white" />
                    </div>
                    <div>
                      <h2 className="text-2xl font-bold text-white">Cashback Optimizer</h2>
                      <p className="text-white/70">AI-powered reward maximization</p>
                    </div>
                  </div>
                </div>
                <div className="p-8">
                  <SmartCashbackOptimizer />
                </div>
              </div>
            </div>
          </div>

          {/* Right Sidebar - Floating Cards */}
          <div className="lg:col-span-4 space-y-8">
            {/* Smart Suggestions - Cyberpunk */}
            <div className={`group transition-all duration-1000 delay-1400 ${mounted ? 'translate-y-0 opacity-100' : 'translate-y-8 opacity-0'}`}>
              <div className="bg-white/10 backdrop-blur-xl rounded-3xl shadow-2xl border border-white/20 hover:bg-white/20 transition-all duration-500 transform hover:scale-[1.02] hover:shadow-yellow-500/25">
                <div className="px-6 py-5 border-b border-white/20">
                  <div className="flex items-center gap-3">
                    <div className="w-10 h-10 bg-gradient-to-r from-yellow-400 to-orange-500 rounded-xl flex items-center justify-center shadow-lg group-hover:animate-pulse">
                      <Sparkles className="h-5 w-5 text-white" />
                    </div>
                    <div>
                      <h2 className="text-xl font-bold text-white">Smart Insights</h2>
                      <p className="text-white/70 text-sm">AI recommendations</p>
                    </div>
                  </div>
                </div>
                <div className="p-6">
                  <SmartSuggestions />
                </div>
              </div>
            </div>

            {/* Achievement Badges - Neon */}
            <div className={`group transition-all duration-1000 delay-1600 ${mounted ? 'translate-y-0 opacity-100' : 'translate-y-8 opacity-0'}`}>
              <div className="bg-white/10 backdrop-blur-xl rounded-3xl shadow-2xl border border-white/20 hover:bg-white/20 transition-all duration-500 transform hover:scale-[1.02] hover:shadow-pink-500/25">
                <div className="px-6 py-5 border-b border-white/20">
                  <div className="flex items-center gap-3">
                    <div className="w-10 h-10 bg-gradient-to-r from-pink-400 to-rose-500 rounded-xl flex items-center justify-center shadow-lg group-hover:animate-pulse">
                      <Sparkles className="h-5 w-5 text-white" />
                    </div>
                    <div>
                      <h2 className="text-xl font-bold text-white">Achievements</h2>
                      <p className="text-white/70 text-sm">Progress & milestones</p>
                    </div>
                  </div>
                </div>
                <div className="p-6">
                  <BadgesSection />
                </div>
              </div>
            </div>
          </div>
        </div>

        {/* Futuristic Footer */}
        <footer className={`mt-16 transition-all duration-1000 delay-2000 ${mounted ? 'translate-y-0 opacity-100' : 'translate-y-8 opacity-0'}`}>
          <div className="relative group">
            <div className="absolute -inset-1 bg-gradient-to-r from-cyan-400 via-purple-500 to-pink-500 rounded-2xl blur opacity-25 group-hover:opacity-75 transition duration-300 animate-gradient"></div>
            <div className="relative bg-white/10 backdrop-blur-xl rounded-2xl border border-white/20 p-6">
              <div className="flex items-center justify-center gap-4 text-white/70">
                <div className="flex items-center gap-2">
                  <div className="w-2 h-2 bg-green-400 rounded-full animate-pulse"></div>
                  <span className="text-sm">Neural Network Active</span>
                </div>
                <div className="w-px h-4 bg-white/20"></div>
                <div className="flex items-center gap-2">
                  <div className="w-2 h-2 bg-blue-400 rounded-full animate-pulse animation-delay-2000"></div>
                  <span className="text-sm">Quantum Processing</span>
                </div>
                <div className="w-px h-4 bg-white/20"></div>
                <div className="flex items-center gap-2">
                  <div className="w-2 h-2 bg-purple-400 rounded-full animate-pulse animation-delay-4000"></div>
                  <span className="text-sm">AI Optimization</span>
                </div>
              </div>
              <div className="text-center mt-4">
                <p className="text-white/60 text-xs">
                  © 2024 FinoVators • Powered by Advanced AI • Secured by Quantum Encryption
                </p>
              </div>
            </div>
          </div>
        </footer>
      </div>
    </div>
  );
};

export default Index;
