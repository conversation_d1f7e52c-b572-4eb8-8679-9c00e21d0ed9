
import React, { useState, useEffect } from 'react';
import { useDateContext } from '../contexts/DateContext';
import CalendarSelector from '../components/CalendarSelector';
import CreditCardHeader from '../components/CreditCardHeader';
import PaymentSummary from '../components/PaymentSummary';
import BadgesSection from '../components/BadgesSection';
import SmartSuggestions from '../components/SmartSuggestions';
import TransactionMap from '../components/TransactionMap';
import SmartCashbackOptimizer from '../components/SmartCashbackOptimizer';
import { Loader2, Bell, Settings, User, Search, Menu, Sparkles, TrendingUp, Zap } from 'lucide-react';

const Index = () => {
  const { selectedDate, setSelectedDate, isLoading } = useDateContext();
  const [mounted, setMounted] = useState(false);

  useEffect(() => {
    setMounted(true);
  }, []);

  return (
    <div className="min-h-screen bg-gradient-to-br from-slate-900 via-purple-900 to-slate-900 relative overflow-hidden">
      {/* Animated Background */}
      <div className="absolute inset-0 overflow-hidden">
        <div className="absolute -inset-10 opacity-50">
          <div className="absolute top-1/4 left-1/4 w-96 h-96 bg-purple-500 rounded-full mix-blend-multiply filter blur-xl animate-blob"></div>
          <div className="absolute top-1/3 right-1/4 w-96 h-96 bg-yellow-500 rounded-full mix-blend-multiply filter blur-xl animate-blob animation-delay-2000"></div>
          <div className="absolute bottom-1/4 left-1/3 w-96 h-96 bg-pink-500 rounded-full mix-blend-multiply filter blur-xl animate-blob animation-delay-4000"></div>
        </div>
      </div>

      {/* Floating Particles */}
      <div className="absolute inset-0 overflow-hidden pointer-events-none">
        {[...Array(20)].map((_, i) => (
          <div
            key={i}
            className="absolute w-2 h-2 bg-white/20 rounded-full animate-float"
            style={{
              left: `${Math.random() * 100}%`,
              top: `${Math.random() * 100}%`,
              animationDelay: `${Math.random() * 5}s`,
              animationDuration: `${3 + Math.random() * 4}s`
            }}
          />
        ))}
      </div>

      {/* Glassmorphism Navigation */}
      <nav className="relative z-50 backdrop-blur-xl bg-white/10 border-b border-white/20 sticky top-0">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="flex justify-between items-center h-16">
            {/* Logo and Brand */}
            <div className={`flex items-center transition-all duration-1000 ${mounted ? 'translate-x-0 opacity-100' : '-translate-x-10 opacity-0'}`}>
              <div className="flex-shrink-0 flex items-center">
                <div className="w-10 h-10 bg-gradient-to-r from-cyan-400 via-purple-500 to-pink-500 rounded-xl flex items-center justify-center shadow-lg transform hover:scale-110 transition-transform duration-300">
                  <Sparkles className="text-white h-5 w-5 animate-pulse" />
                </div>
                <span className="ml-3 text-2xl font-bold bg-gradient-to-r from-cyan-400 to-purple-400 bg-clip-text text-transparent">
                  FinoVators
                </span>
              </div>
            </div>

            {/* Futuristic Search Bar */}
            <div className={`hidden md:block flex-1 max-w-lg mx-8 transition-all duration-1000 delay-300 ${mounted ? 'translate-y-0 opacity-100' : 'translate-y-4 opacity-0'}`}>
              <div className="relative group">
                <div className="absolute inset-y-0 left-0 pl-4 flex items-center pointer-events-none">
                  <Search className="h-5 w-5 text-white/60 group-focus-within:text-cyan-400 transition-colors duration-300" />
                </div>
                <input
                  type="text"
                  className="block w-full pl-12 pr-4 py-3 bg-white/10 backdrop-blur-md border border-white/20 rounded-2xl leading-5 text-white placeholder-white/60 focus:outline-none focus:ring-2 focus:ring-cyan-400/50 focus:border-cyan-400/50 transition-all duration-300 hover:bg-white/20"
                  placeholder="Search transactions, merchants..."
                />
                <div className="absolute inset-0 rounded-2xl bg-gradient-to-r from-cyan-400/20 to-purple-400/20 opacity-0 group-focus-within:opacity-100 transition-opacity duration-300 pointer-events-none"></div>
              </div>
            </div>

            {/* Animated Action Buttons */}
            <div className={`flex items-center space-x-3 transition-all duration-1000 delay-500 ${mounted ? 'translate-x-0 opacity-100' : 'translate-x-10 opacity-0'}`}>
              <button className="p-3 text-white/70 hover:text-white hover:bg-white/20 rounded-xl transition-all duration-300 transform hover:scale-110 group">
                <Bell className="h-5 w-5 group-hover:animate-bounce" />
                <div className="absolute -top-1 -right-1 w-3 h-3 bg-red-500 rounded-full animate-pulse"></div>
              </button>
              <button className="p-3 text-white/70 hover:text-white hover:bg-white/20 rounded-xl transition-all duration-300 transform hover:scale-110 group">
                <Settings className="h-5 w-5 group-hover:animate-spin" />
              </button>
              <div className="flex items-center space-x-3 bg-white/10 backdrop-blur-md rounded-2xl px-4 py-2 border border-white/20 hover:bg-white/20 transition-all duration-300">
                <div className="w-8 h-8 bg-gradient-to-r from-cyan-400 to-purple-500 rounded-full flex items-center justify-center shadow-lg animate-pulse">
                  <User className="h-4 w-4 text-white" />
                </div>
                <span className="hidden md:block text-sm font-medium text-white">John Smith</span>
              </div>
            </div>
          </div>
        </div>
      </nav>

      {/* Main Content */}
      <div className="relative z-10 max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
        {/* Animated Page Header */}
        <div className={`mb-12 text-center transition-all duration-1000 delay-700 ${mounted ? 'translate-y-0 opacity-100' : 'translate-y-8 opacity-0'}`}>
          <div className="relative inline-block">
            <h1 className="text-6xl font-bold bg-gradient-to-r from-cyan-400 via-purple-400 to-pink-400 bg-clip-text text-transparent mb-4 animate-gradient">
              Financial Dashboard
            </h1>
            <div className="absolute -top-2 -right-2">
              <Zap className="h-8 w-8 text-yellow-400 animate-bounce" />
            </div>
          </div>
          <p className="text-xl text-white/80 max-w-2xl mx-auto leading-relaxed">
            Experience the future of financial management with AI-powered insights and stunning visualizations
          </p>
          <div className="flex justify-center items-center gap-2 mt-4">
            <TrendingUp className="h-5 w-5 text-green-400 animate-pulse" />
            <span className="text-green-400 font-semibold">Live Data • Real-time Updates</span>
          </div>
        </div>

        {/* Floating Calendar Selector */}
        <div className={`mb-12 transition-all duration-1000 delay-900 ${mounted ? 'translate-y-0 opacity-100' : 'translate-y-8 opacity-0'}`}>
          <CalendarSelector
            selectedDate={selectedDate}
            onDateChange={setSelectedDate}
          />
        </div>

        {/* Futuristic Loading Overlay */}
        {isLoading && (
          <div className="fixed inset-0 bg-black/60 backdrop-blur-xl z-50 flex items-center justify-center">
            <div className="bg-white/10 backdrop-blur-xl rounded-3xl p-8 shadow-2xl border border-white/20 flex items-center gap-4">
              <div className="relative">
                <Loader2 className="h-8 w-8 animate-spin text-cyan-400" />
                <div className="absolute inset-0 h-8 w-8 border-2 border-purple-400/30 rounded-full animate-ping"></div>
              </div>
              <div>
                <span className="text-lg font-semibold text-white">Loading Financial Data</span>
                <div className="flex gap-1 mt-2">
                  <div className="w-2 h-2 bg-cyan-400 rounded-full animate-bounce"></div>
                  <div className="w-2 h-2 bg-purple-400 rounded-full animate-bounce" style={{animationDelay: '0.1s'}}></div>
                  <div className="w-2 h-2 bg-pink-400 rounded-full animate-bounce" style={{animationDelay: '0.2s'}}></div>
                </div>
              </div>
            </div>
          </div>
        )}

        {/* Floating Credit Card */}
        <div className={`mb-12 transition-all duration-1000 delay-1100 ${mounted ? 'translate-y-0 opacity-100' : 'translate-y-8 opacity-0'}`}>
          <CreditCardHeader />
        </div>

        {/* Animated Dashboard Grid */}
        <div className="grid grid-cols-1 lg:grid-cols-12 gap-8">
          {/* Left Column - Main Content */}
          <div className="lg:col-span-8 space-y-8">
            {/* Payment Overview - Glassmorphism */}
            <div className={`group transition-all duration-1000 delay-1300 ${mounted ? 'translate-y-0 opacity-100' : 'translate-y-8 opacity-0'}`}>
              <div className="bg-white/10 backdrop-blur-xl rounded-3xl shadow-2xl border border-white/20 hover:bg-white/20 transition-all duration-500 transform hover:scale-[1.02] hover:shadow-cyan-500/25">
                <div className="px-8 py-6 border-b border-white/20">
                  <div className="flex items-center gap-3">
                    <div className="w-12 h-12 bg-gradient-to-r from-cyan-400 to-blue-500 rounded-2xl flex items-center justify-center shadow-lg group-hover:animate-pulse">
                      <TrendingUp className="h-6 w-6 text-white" />
                    </div>
                    <div>
                      <h2 className="text-2xl font-bold text-white">Payment Overview</h2>
                      <p className="text-white/70">Your financial snapshot with AI insights</p>
                    </div>
                  </div>
                </div>
                <div className="p-8">
                  <PaymentSummary />
                </div>
              </div>
            </div>

            {/* Transaction Map - Neon Glow */}
            <div className={`group transition-all duration-1000 delay-1500 ${mounted ? 'translate-y-0 opacity-100' : 'translate-y-8 opacity-0'}`}>
              <div className="bg-white/10 backdrop-blur-xl rounded-3xl shadow-2xl border border-white/20 hover:bg-white/20 transition-all duration-500 transform hover:scale-[1.02] hover:shadow-purple-500/25">
                <div className="px-8 py-6 border-b border-white/20">
                  <div className="flex items-center gap-3">
                    <div className="w-12 h-12 bg-gradient-to-r from-purple-400 to-pink-500 rounded-2xl flex items-center justify-center shadow-lg group-hover:animate-pulse">
                      <Search className="h-6 w-6 text-white" />
                    </div>
                    <div>
                      <h2 className="text-2xl font-bold text-white">Transaction Map</h2>
                      <p className="text-white/70">Interactive spending visualization</p>
                    </div>
                  </div>
                </div>
                <div className="p-8">
                  <TransactionMap />
                </div>
              </div>
            </div>

            {/* Cashback Optimizer - Holographic */}
            <div className={`group transition-all duration-1000 delay-1700 ${mounted ? 'translate-y-0 opacity-100' : 'translate-y-8 opacity-0'}`}>
              <div className="bg-white/10 backdrop-blur-xl rounded-3xl shadow-2xl border border-white/20 hover:bg-white/20 transition-all duration-500 transform hover:scale-[1.02] hover:shadow-green-500/25">
                <div className="px-8 py-6 border-b border-white/20">
                  <div className="flex items-center gap-3">
                    <div className="w-12 h-12 bg-gradient-to-r from-green-400 to-emerald-500 rounded-2xl flex items-center justify-center shadow-lg group-hover:animate-pulse">
                      <Zap className="h-6 w-6 text-white" />
                    </div>
                    <div>
                      <h2 className="text-2xl font-bold text-white">Cashback Optimizer</h2>
                      <p className="text-white/70">AI-powered reward maximization</p>
                    </div>
                  </div>
                </div>
                <div className="p-8">
                  <SmartCashbackOptimizer />
                </div>
              </div>
            </div>
          </div>

          {/* Right Sidebar - Floating Cards */}
          <div className="lg:col-span-4 space-y-8">
            {/* Smart Suggestions - Cyberpunk */}
            <div className={`group transition-all duration-1000 delay-1400 ${mounted ? 'translate-y-0 opacity-100' : 'translate-y-8 opacity-0'}`}>
              <div className="bg-white/10 backdrop-blur-xl rounded-3xl shadow-2xl border border-white/20 hover:bg-white/20 transition-all duration-500 transform hover:scale-[1.02] hover:shadow-yellow-500/25">
                <div className="px-6 py-5 border-b border-white/20">
                  <div className="flex items-center gap-3">
                    <div className="w-10 h-10 bg-gradient-to-r from-yellow-400 to-orange-500 rounded-xl flex items-center justify-center shadow-lg group-hover:animate-pulse">
                      <Sparkles className="h-5 w-5 text-white" />
                    </div>
                    <div>
                      <h2 className="text-xl font-bold text-white">Smart Insights</h2>
                      <p className="text-white/70 text-sm">AI recommendations</p>
                    </div>
                  </div>
                </div>
                <div className="p-6">
                  <SmartSuggestions />
                </div>
              </div>
            </div>

            {/* Achievement Badges - Neon */}
            <div className={`group transition-all duration-1000 delay-1600 ${mounted ? 'translate-y-0 opacity-100' : 'translate-y-8 opacity-0'}`}>
              <div className="bg-white/10 backdrop-blur-xl rounded-3xl shadow-2xl border border-white/20 hover:bg-white/20 transition-all duration-500 transform hover:scale-[1.02] hover:shadow-pink-500/25">
                <div className="px-6 py-5 border-b border-white/20">
                  <div className="flex items-center gap-3">
                    <div className="w-10 h-10 bg-gradient-to-r from-pink-400 to-rose-500 rounded-xl flex items-center justify-center shadow-lg group-hover:animate-pulse">
                      <Sparkles className="h-5 w-5 text-white" />
                    </div>
                    <div>
                      <h2 className="text-xl font-bold text-white">Achievements</h2>
                      <p className="text-white/70 text-sm">Progress & milestones</p>
                    </div>
                  </div>
                </div>
                <div className="p-6">
                  <BadgesSection />
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
};

export default Index;
