
import React from 'react';
import { useDateContext } from '../contexts/DateContext';
import CalendarSelector from '../components/CalendarSelector';
import CreditCardHeader from '../components/CreditCardHeader';
import PaymentSummary from '../components/PaymentSummary';
import BadgesSection from '../components/BadgesSection';
import SmartSuggestions from '../components/SmartSuggestions';
import TransactionMap from '../components/TransactionMap';
import SmartCashbackOptimizer from '../components/SmartCashbackOptimizer';
import BadgeGallery from '@/components/BadgeGallery';

const Index = () => {
  const { selectedDate, setSelectedDate, isLoading } = useDateContext();

  return (
    <div className="min-h-screen bg-gray-50">
      {/* Top Navigation Bar */}
      <nav className="bg-white border-b border-gray-200 sticky top-0 z-50 shadow-sm">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="flex justify-between items-center h-16">
            {/* Logo and Brand */}
            <div className="flex items-center">
              <div className="flex-shrink-0 flex items-center">
                <div className="w-8 h-8 bg-gradient-to-r from-blue-600 to-purple-600 rounded-lg flex items-center justify-center">
                  <span className="text-white font-bold text-sm">F</span>
                </div>
                <span className="ml-3 text-xl font-semibold text-gray-900">FinoVators</span>
              </div>
            </div>

        {/* Credit Card Header */}
        <CreditCardHeader />
        
        {/* Payment Summary Section */}
        <section className="mb-8">
          <div className="bg-white/80 backdrop-blur-sm rounded-2xl p-6 border border-white/20 shadow-lg">
            <h2 className="text-2xl font-bold text-gray-800 mb-4 flex items-center gap-2">
              💳 Payment Overview
            </h2>
            <PaymentSummary />
          </div>
        </section>
        
        {/* Badges Section */}
        <section className="mb-8">
          <div className="bg-white/80 backdrop-blur-sm rounded-2xl p-6 border border-white/20 shadow-lg">
            <h2 className="text-2xl font-bold text-gray-800 mb-4 flex items-center gap-2">
              🏆 Achievement Badges
            </h2>
            <BadgeGallery />
          </div>
        </section>
        
        {/* Smart Suggestions Section */}
        <section className="mb-8">
          <div className="bg-white/80 backdrop-blur-sm rounded-2xl p-6 border border-white/20 shadow-lg">
            <h2 className="text-2xl font-bold text-gray-800 mb-4 flex items-center gap-2">
              💡 Smart Suggestions
            </h2>
            <SmartSuggestions />
          </div>
        </section>
        
        {/* Smart Cashback Optimizer Section */}
        <section className="mb-8">
          <div className="bg-white/80 backdrop-blur-sm rounded-2xl p-6 border border-white/20 shadow-lg">
            <h2 className="text-2xl font-bold text-gray-800 mb-4 flex items-center gap-2">
              🎯 Smart Cashback Optimizer
            </h2>
            <SmartCashbackOptimizer />
          </div>
        </section>

            {/* Right side actions */}
            <div className="flex items-center space-x-4">
              <button className="p-2 text-gray-400 hover:text-gray-500 hover:bg-gray-100 rounded-lg transition-colors">
                <Bell className="h-5 w-5" />
              </button>
              <button className="p-2 text-gray-400 hover:text-gray-500 hover:bg-gray-100 rounded-lg transition-colors">
                <Settings className="h-5 w-5" />
              </button>
              <div className="flex items-center space-x-3">
                <div className="w-8 h-8 bg-gradient-to-r from-blue-500 to-purple-500 rounded-full flex items-center justify-center">
                  <User className="h-4 w-4 text-white" />
                </div>
                <span className="hidden md:block text-sm font-medium text-gray-700">Rajesh Kumar</span>
              </div>
            </div>
          </div>
        </div>
      </nav>

      {/* Main Content */}
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
        {/* Page Header */}
        <div className="mb-8">
          <h1 className="text-3xl font-bold text-gray-900 mb-2">Dashboard</h1>
          <p className="text-gray-600">Monitor your spending, track rewards, and optimize your financial health in India</p>
        </div>

        {/* Calendar Selector */}
        <div className="mb-8">
          <CalendarSelector
            selectedDate={selectedDate}
            onDateChange={setSelectedDate}
          />
        </div>

        {/* Loading Overlay */}
        {isLoading && (
          <div className="fixed inset-0 bg-black/50 backdrop-blur-sm z-50 flex items-center justify-center">
            <div className="bg-white rounded-xl p-6 shadow-2xl flex items-center gap-3">
              <Loader2 className="h-6 w-6 animate-spin text-blue-600" />
              <span className="text-sm font-medium text-gray-900">Loading data...</span>
            </div>
          </div>
        )}

        {/* Credit Card Section */}
        <div className="mb-8">
          <CreditCardHeader />
        </div>

        {/* Main Dashboard Grid */}
        <div className="grid grid-cols-1 lg:grid-cols-12 gap-6">
          {/* Left Column - Main Content */}
          <div className="lg:col-span-8 space-y-6">
            {/* Payment Overview */}
            <div className="bg-white rounded-xl shadow-sm border border-gray-200">
              <div className="px-6 py-4 border-b border-gray-200">
                <h2 className="text-lg font-semibold text-gray-900">Payment Overview</h2>
                <p className="text-sm text-gray-500">Your current financial snapshot</p>
              </div>
              <div className="p-6">
                <PaymentSummary />
              </div>
            </div>

            {/* Transaction Map */}
            <div className="bg-white rounded-xl shadow-sm border border-gray-200">
              <div className="px-6 py-4 border-b border-gray-200">
                <h2 className="text-lg font-semibold text-gray-900">Transaction Map</h2>
                <p className="text-sm text-gray-500">Visualize your spending locations</p>
              </div>
              <div className="p-6">
                <TransactionMap />
              </div>
            </div>

            {/* Cashback Optimizer */}
            <div className="bg-white rounded-xl shadow-sm border border-gray-200">
              <div className="px-6 py-4 border-b border-gray-200">
                <h2 className="text-lg font-semibold text-gray-900">Cashback Optimizer</h2>
                <p className="text-sm text-gray-500">Maximize your rewards potential</p>
              </div>
              <div className="p-6">
                <SmartCashbackOptimizer />
              </div>
            </div>
          </div>

          {/* Right Sidebar */}
          <div className="lg:col-span-4 space-y-6">
            {/* Smart Suggestions */}
            <div className="bg-white rounded-xl shadow-sm border border-gray-200">
              <div className="px-6 py-4 border-b border-gray-200">
                <h2 className="text-lg font-semibold text-gray-900">Smart Insights</h2>
                <p className="text-sm text-gray-500">AI-powered recommendations</p>
              </div>
              <div className="p-6">
                <SmartSuggestions />
              </div>
            </div>

            {/* Achievement Badges */}
            <div className="bg-white rounded-xl shadow-sm border border-gray-200">
              <div className="px-6 py-4 border-b border-gray-200">
                <h2 className="text-lg font-semibold text-gray-900">Achievements</h2>
                <p className="text-sm text-gray-500">Your progress and milestones</p>
              </div>
              <div className="p-6">
                <BadgesSection />
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
};

export default Index;
