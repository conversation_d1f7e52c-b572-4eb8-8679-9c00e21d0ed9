
import React from 'react';
import { useDateContext } from '../contexts/DateContext';
import CalendarSelector from '../components/CalendarSelector';
import CreditCardHeader from '../components/CreditCardHeader';
import PaymentSummary from '../components/PaymentSummary';
import BadgesSection from '../components/BadgesSection';
import SmartSuggestions from '../components/SmartSuggestions';
import TransactionMap from '../components/TransactionMap';
import SmartCashbackOptimizer from '../components/SmartCashbackOptimizer';
import { Loader2, TrendingUp, CreditCard, Award, Lightbulb, MapPin, Target } from 'lucide-react';

const Index = () => {
  const { selectedDate, setSelectedDate, isLoading } = useDateContext();

  return (
    <div className="min-h-screen bg-gradient-to-br from-slate-50 via-blue-50 to-indigo-100">
      <div className="container mx-auto px-4 py-6">
        {/* Enhanced Header */}
        <header className="mb-8 text-center relative">
          <div className="absolute inset-0 bg-gradient-to-r from-blue-600/10 via-purple-600/10 to-teal-600/10 rounded-3xl blur-3xl"></div>
          <div className="relative">
            <h1 className="text-6xl font-bold bg-gradient-to-r from-blue-600 via-purple-600 to-teal-600 bg-clip-text text-transparent mb-4">
              FinoVators Dashboard
            </h1>
            <p className="text-xl text-gray-700 max-w-3xl mx-auto leading-relaxed">
              🚀 Your intelligent financial companion - Track spending, maximize rewards, and optimize your financial journey
            </p>
          </div>
        </header>

        {/* Calendar Selector */}
        <section className="mb-8">
          <CalendarSelector
            selectedDate={selectedDate}
            onDateChange={setSelectedDate}
          />
        </section>

        {/* Loading Overlay */}
        {isLoading && (
          <div className="fixed inset-0 bg-black/20 backdrop-blur-sm z-50 flex items-center justify-center">
            <div className="bg-white rounded-2xl p-8 shadow-2xl flex items-center gap-4">
              <Loader2 className="h-8 w-8 animate-spin text-blue-600" />
              <span className="text-lg font-semibold text-gray-800">Loading data...</span>
            </div>
          </div>
        )}

        {/* Credit Card Header */}
        <CreditCardHeader />

        {/* Main Content Grid */}
        <div className="grid grid-cols-1 xl:grid-cols-3 gap-8 mb-8">
          {/* Left Column - Primary Info */}
          <div className="xl:col-span-2 space-y-8">
            {/* Payment Summary Section */}
            <section>
              <div className="bg-white/90 backdrop-blur-sm rounded-3xl p-8 border border-white/30 shadow-xl hover:shadow-2xl transition-all duration-300">
                <h2 className="text-3xl font-bold text-gray-800 mb-6 flex items-center gap-3">
                  <div className="p-2 bg-blue-100 rounded-xl">
                    <CreditCard className="h-7 w-7 text-blue-600" />
                  </div>
                  Payment Overview
                </h2>
                <PaymentSummary />
              </div>
            </section>

            {/* Transaction Map Section */}
            <section>
              <div className="bg-white/90 backdrop-blur-sm rounded-3xl p-8 border border-white/30 shadow-xl hover:shadow-2xl transition-all duration-300">
                <h2 className="text-3xl font-bold text-gray-800 mb-6 flex items-center gap-3">
                  <div className="p-2 bg-teal-100 rounded-xl">
                    <MapPin className="h-7 w-7 text-teal-600" />
                  </div>
                  Transaction Map
                </h2>
                <TransactionMap />
              </div>
            </section>
          </div>

          {/* Right Column - Secondary Info */}
          <div className="space-y-8">
            {/* Smart Suggestions Section */}
            <section>
              <div className="bg-white/90 backdrop-blur-sm rounded-3xl p-8 border border-white/30 shadow-xl hover:shadow-2xl transition-all duration-300">
                <h2 className="text-2xl font-bold text-gray-800 mb-6 flex items-center gap-3">
                  <div className="p-2 bg-purple-100 rounded-xl">
                    <Lightbulb className="h-6 w-6 text-purple-600" />
                  </div>
                  Smart Suggestions
                </h2>
                <SmartSuggestions />
              </div>
            </section>

            {/* Badges Section */}
            <section>
              <div className="bg-white/90 backdrop-blur-sm rounded-3xl p-8 border border-white/30 shadow-xl hover:shadow-2xl transition-all duration-300">
                <h2 className="text-2xl font-bold text-gray-800 mb-6 flex items-center gap-3">
                  <div className="p-2 bg-yellow-100 rounded-xl">
                    <Award className="h-6 w-6 text-yellow-600" />
                  </div>
                  Achievement Badges
                </h2>
                <BadgesSection />
              </div>
            </section>
          </div>
        </div>

        {/* Smart Cashback Optimizer Section - Full Width */}
        <section className="mb-8">
          <div className="bg-white/90 backdrop-blur-sm rounded-3xl p-8 border border-white/30 shadow-xl hover:shadow-2xl transition-all duration-300">
            <h2 className="text-3xl font-bold text-gray-800 mb-6 flex items-center gap-3">
              <div className="p-2 bg-green-100 rounded-xl">
                <Target className="h-7 w-7 text-green-600" />
              </div>
              Smart Cashback Optimizer
            </h2>
            <SmartCashbackOptimizer />
          </div>
        </section>
      </div>
    </div>
  );
};

export default Index;
