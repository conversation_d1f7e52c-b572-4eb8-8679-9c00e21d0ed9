
import React, { useState, useEffect } from 'react';
import { useDateContext } from '../contexts/DateContext';
// import CalendarSelector from '../components/CalendarSelector';
import CreditCardHeader from '../components/CreditCardHeader';
import PaymentSummary from '../components/PaymentSummary';
import BadgesSection from '../components/BadgesSection';
import SmartSuggestions from '../components/SmartSuggestions';
import TransactionMap from '../components/TransactionMap';
import SmartCashbackOptimizer from '../components/SmartCashbackOptimizer';
import { Loader2, Bell, Settings, User, Search, Menu, Sparkles, TrendingUp, Zap, CalendarDays, Calendar } from 'lucide-react';
import { format } from 'date-fns';

// Simple inline CalendarSelector
const CalendarSelector = ({ selectedDate, onDateChange }: { selectedDate: Date; onDateChange: (date: Date) => void }) => {
  return (
    <div className="bg-white rounded-2xl shadow-sm border border-gray-200 overflow-hidden">
      <div className="px-6 py-4 border-b border-gray-100 bg-gradient-to-r from-blue-50 to-indigo-50">
        <div className="flex items-center justify-between">
          <div className="flex items-center gap-3">
            <div className="w-10 h-10 bg-gradient-to-r from-blue-600 to-indigo-600 rounded-xl flex items-center justify-center shadow-sm">
              <CalendarDays className="h-5 w-5 text-white" />
            </div>
            <div>
              <h3 className="text-lg font-semibold text-gray-900">Period Selector</h3>
              <p className="text-gray-600 text-sm">Choose reporting period</p>
            </div>
          </div>

          <div className="flex items-center gap-4">
            <div className="text-right">
              <div className="text-xl font-bold text-gray-900">
                {format(selectedDate, 'MMMM yyyy')}
              </div>
              <div className="text-gray-500 text-sm">Selected Period</div>
            </div>
            <button className="bg-blue-600 hover:bg-blue-700 text-white px-4 py-2 rounded-lg font-medium transition-colors duration-200 flex items-center gap-2">
              <Calendar className="h-4 w-4" />
              Change Period
            </button>
          </div>
        </div>
      </div>
    </div>
  );
};

const Index = () => {
  const { selectedDate, setSelectedDate, isLoading } = useDateContext();
  const [mounted, setMounted] = useState(false);

  useEffect(() => {
    setMounted(true);
  }, []);

  return (
    <div className="min-h-screen bg-gradient-to-br from-gray-50 to-blue-50 relative">
      {/* Professional Background */}
      <div className="absolute inset-0 bg-gradient-to-br from-gray-50 to-blue-50">
        {/* Subtle geometric patterns */}
        <div className="absolute inset-0 opacity-5">
          <div className="absolute top-0 left-0 w-full h-full" style={{
            backgroundImage: `radial-gradient(circle at 25% 25%, #3b82f6 2px, transparent 2px),
                             radial-gradient(circle at 75% 75%, #6366f1 2px, transparent 2px)`,
            backgroundSize: '60px 60px'
          }}></div>
        </div>
      </div>

      {/* Professional Navigation */}
      <nav className="relative z-50 bg-white/95 backdrop-blur-sm border-b border-gray-200 sticky top-0 shadow-sm">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="flex justify-between items-center h-16">
            {/* Logo and Brand */}
            <div className="flex items-center gap-3">
              <div className="w-10 h-10 bg-gradient-to-r from-blue-600 to-indigo-600 rounded-xl flex items-center justify-center shadow-md">
                <TrendingUp className="h-6 w-6 text-white" />
              </div>
              <div>
                <h1 className="text-xl font-bold text-gray-900">
                  FinoVators
                </h1>
                <p className="text-xs text-gray-500">Financial Intelligence Platform</p>
              </div>
            </div>

            {/* Professional Search Bar */}
            <div className="flex-1 max-w-md mx-8">
              <div className="relative">
                <div className="flex items-center bg-gray-50 rounded-xl border border-gray-200 px-4 py-2 focus-within:ring-2 focus-within:ring-blue-500 focus-within:border-blue-500 transition-all">
                  <Search className="h-4 w-4 text-gray-400 mr-3" />
                  <input
                    type="text"
                    placeholder="Search transactions, insights..."
                    className="flex-1 bg-transparent text-gray-900 placeholder-gray-500 text-sm focus:outline-none"
                  />
                </div>
              </div>
            </div>

            {/* Professional Action Buttons */}
            <div className="flex items-center gap-3">
              <button className="relative p-2 bg-gray-50 rounded-xl border border-gray-200 hover:bg-gray-100 transition-all duration-200 group">
                <Bell className="h-5 w-5 text-gray-600 group-hover:text-gray-900" />
                <div className="absolute -top-1 -right-1 w-3 h-3 bg-red-500 rounded-full"></div>
              </button>
              <button className="p-2 bg-gray-50 rounded-xl border border-gray-200 hover:bg-gray-100 transition-all duration-200 group">
                <Settings className="h-5 w-5 text-gray-600 group-hover:text-gray-900" />
              </button>
              <button className="p-2 bg-gray-50 rounded-xl border border-gray-200 hover:bg-gray-100 transition-all duration-200 group">
                <User className="h-5 w-5 text-gray-600 group-hover:text-gray-900" />
              </button>
            </div>
          </div>
        </div>
      </nav>

      {/* Main Content */}
      <div className="relative z-10 max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
        {/* Animated Page Header */}
        <div className={`mb-12 text-center transition-all duration-1000 delay-700 ${mounted ? 'translate-y-0 opacity-100' : 'translate-y-8 opacity-0'}`}>
          <div className="relative inline-block">
            <h1 className="text-6xl font-bold bg-gradient-to-r from-cyan-400 via-purple-400 to-pink-400 bg-clip-text text-transparent mb-4 animate-gradient">
              Financial Dashboard
            </h1>
            <div className="absolute -top-2 -right-2">
              <Zap className="h-8 w-8 text-yellow-400 animate-bounce" />
            </div>
          </div>
          <p className="text-xl text-white/80 max-w-2xl mx-auto leading-relaxed">
            Experience the future of financial management with AI-powered insights and stunning visualizations
          </p>
          <div className="flex justify-center items-center gap-2 mt-4">
            <TrendingUp className="h-5 w-5 text-green-400 animate-pulse" />
            <span className="text-green-400 font-semibold">Live Data • Real-time Updates</span>
          </div>
        </div>

        {/* Floating Calendar Selector */}
        <div className={`mb-12 transition-all duration-1000 delay-900 ${mounted ? 'translate-y-0 opacity-100' : 'translate-y-8 opacity-0'}`}>
          <CalendarSelector
            selectedDate={selectedDate}
            onDateChange={setSelectedDate}
          />
        </div>

        {/* Futuristic Loading Overlay */}
        {isLoading && (
          <div className="fixed inset-0 bg-black/60 backdrop-blur-xl z-50 flex items-center justify-center">
            <div className="bg-white/10 backdrop-blur-xl rounded-3xl p-8 shadow-2xl border border-white/20 flex items-center gap-4">
              <div className="relative">
                <Loader2 className="h-8 w-8 animate-spin text-cyan-400" />
                <div className="absolute inset-0 h-8 w-8 border-2 border-purple-400/30 rounded-full animate-ping"></div>
              </div>
              <div>
                <span className="text-lg font-semibold text-white">Loading Financial Data</span>
                <div className="flex gap-1 mt-2">
                  <div className="w-2 h-2 bg-cyan-400 rounded-full animate-bounce"></div>
                  <div className="w-2 h-2 bg-purple-400 rounded-full animate-bounce" style={{animationDelay: '0.1s'}}></div>
                  <div className="w-2 h-2 bg-pink-400 rounded-full animate-bounce" style={{animationDelay: '0.2s'}}></div>
                </div>
              </div>
            </div>
          </div>
        )}

        {/* Floating Credit Card */}
        <div className={`mb-12 transition-all duration-1000 delay-1100 ${mounted ? 'translate-y-0 opacity-100' : 'translate-y-8 opacity-0'}`}>
          <CreditCardHeader />
        </div>

        {/* Animated Dashboard Grid */}
        <div className="grid grid-cols-1 lg:grid-cols-12 gap-8">
          {/* Left Column - Main Content */}
          <div className="lg:col-span-8 space-y-8">
            {/* Payment Overview - Glassmorphism */}
            <div className={`group transition-all duration-1000 delay-1300 ${mounted ? 'translate-y-0 opacity-100' : 'translate-y-8 opacity-0'}`}>
              <div className="bg-white/10 backdrop-blur-xl rounded-3xl shadow-2xl border border-white/20 hover:bg-white/20 transition-all duration-500 transform hover:scale-[1.02] hover:shadow-cyan-500/25">
                <div className="px-8 py-6 border-b border-white/20">
                  <div className="flex items-center gap-3">
                    <div className="w-12 h-12 bg-gradient-to-r from-cyan-400 to-blue-500 rounded-2xl flex items-center justify-center shadow-lg group-hover:animate-pulse">
                      <TrendingUp className="h-6 w-6 text-white" />
                    </div>
                    <div>
                      <h2 className="text-2xl font-bold text-white">Payment Overview</h2>
                      <p className="text-white/70">Your financial snapshot with AI insights</p>
                    </div>
                  </div>
                </div>
                <div className="p-8">
                  <PaymentSummary />
                </div>
              </div>
            </div>

            {/* Transaction Map - Neon Glow */}
            <div className={`group transition-all duration-1000 delay-1500 ${mounted ? 'translate-y-0 opacity-100' : 'translate-y-8 opacity-0'}`}>
              <div className="bg-white/10 backdrop-blur-xl rounded-3xl shadow-2xl border border-white/20 hover:bg-white/20 transition-all duration-500 transform hover:scale-[1.02] hover:shadow-purple-500/25">
                <div className="px-8 py-6 border-b border-white/20">
                  <div className="flex items-center gap-3">
                    <div className="w-12 h-12 bg-gradient-to-r from-purple-400 to-pink-500 rounded-2xl flex items-center justify-center shadow-lg group-hover:animate-pulse">
                      <Search className="h-6 w-6 text-white" />
                    </div>
                    <div>
                      <h2 className="text-2xl font-bold text-white">Transaction Map</h2>
                      <p className="text-white/70">Interactive spending visualization</p>
                    </div>
                  </div>
                </div>
                <div className="p-8">
                  <TransactionMap />
                </div>
              </div>
            </div>

            {/* Cashback Optimizer - Holographic */}
            <div className={`group transition-all duration-1000 delay-1700 ${mounted ? 'translate-y-0 opacity-100' : 'translate-y-8 opacity-0'}`}>
              <div className="bg-white/10 backdrop-blur-xl rounded-3xl shadow-2xl border border-white/20 hover:bg-white/20 transition-all duration-500 transform hover:scale-[1.02] hover:shadow-green-500/25">
                <div className="px-8 py-6 border-b border-white/20">
                  <div className="flex items-center gap-3">
                    <div className="w-12 h-12 bg-gradient-to-r from-green-400 to-emerald-500 rounded-2xl flex items-center justify-center shadow-lg group-hover:animate-pulse">
                      <Zap className="h-6 w-6 text-white" />
                    </div>
                    <div>
                      <h2 className="text-2xl font-bold text-white">Cashback Optimizer</h2>
                      <p className="text-white/70">AI-powered reward maximization</p>
                    </div>
                  </div>
                </div>
                <div className="p-8">
                  <SmartCashbackOptimizer />
                </div>
              </div>
            </div>
          </div>

          {/* Right Sidebar - Floating Cards */}
          <div className="lg:col-span-4 space-y-8">
            {/* Smart Suggestions - Cyberpunk */}
            <div className={`group transition-all duration-1000 delay-1400 ${mounted ? 'translate-y-0 opacity-100' : 'translate-y-8 opacity-0'}`}>
              <div className="bg-white/10 backdrop-blur-xl rounded-3xl shadow-2xl border border-white/20 hover:bg-white/20 transition-all duration-500 transform hover:scale-[1.02] hover:shadow-yellow-500/25">
                <div className="px-6 py-5 border-b border-white/20">
                  <div className="flex items-center gap-3">
                    <div className="w-10 h-10 bg-gradient-to-r from-yellow-400 to-orange-500 rounded-xl flex items-center justify-center shadow-lg group-hover:animate-pulse">
                      <Sparkles className="h-5 w-5 text-white" />
                    </div>
                    <div>
                      <h2 className="text-xl font-bold text-white">Smart Insights</h2>
                      <p className="text-white/70 text-sm">AI recommendations</p>
                    </div>
                  </div>
                </div>
                <div className="p-6">
                  <SmartSuggestions />
                </div>
              </div>
            </div>

            {/* Achievement Badges - Neon */}
            <div className={`group transition-all duration-1000 delay-1600 ${mounted ? 'translate-y-0 opacity-100' : 'translate-y-8 opacity-0'}`}>
              <div className="bg-white/10 backdrop-blur-xl rounded-3xl shadow-2xl border border-white/20 hover:bg-white/20 transition-all duration-500 transform hover:scale-[1.02] hover:shadow-pink-500/25">
                <div className="px-6 py-5 border-b border-white/20">
                  <div className="flex items-center gap-3">
                    <div className="w-10 h-10 bg-gradient-to-r from-pink-400 to-rose-500 rounded-xl flex items-center justify-center shadow-lg group-hover:animate-pulse">
                      <Sparkles className="h-5 w-5 text-white" />
                    </div>
                    <div>
                      <h2 className="text-xl font-bold text-white">Achievements</h2>
                      <p className="text-white/70 text-sm">Progress & milestones</p>
                    </div>
                  </div>
                </div>
                <div className="p-6">
                  <BadgesSection />
                </div>
              </div>
            </div>
          </div>
        </div>

        {/* Futuristic Footer */}
        <footer className={`mt-16 transition-all duration-1000 delay-2000 ${mounted ? 'translate-y-0 opacity-100' : 'translate-y-8 opacity-0'}`}>
          <div className="relative group">
            <div className="absolute -inset-1 bg-gradient-to-r from-cyan-400 via-purple-500 to-pink-500 rounded-2xl blur opacity-25 group-hover:opacity-75 transition duration-300 animate-gradient"></div>
            <div className="relative bg-white/10 backdrop-blur-xl rounded-2xl border border-white/20 p-6">
              <div className="flex items-center justify-center gap-4 text-white/70">
                <div className="flex items-center gap-2">
                  <div className="w-2 h-2 bg-green-400 rounded-full animate-pulse"></div>
                  <span className="text-sm">Neural Network Active</span>
                </div>
                <div className="w-px h-4 bg-white/20"></div>
                <div className="flex items-center gap-2">
                  <div className="w-2 h-2 bg-blue-400 rounded-full animate-pulse animation-delay-2000"></div>
                  <span className="text-sm">Quantum Processing</span>
                </div>
                <div className="w-px h-4 bg-white/20"></div>
                <div className="flex items-center gap-2">
                  <div className="w-2 h-2 bg-purple-400 rounded-full animate-pulse animation-delay-4000"></div>
                  <span className="text-sm">AI Optimization</span>
                </div>
              </div>
              <div className="text-center mt-4">
                <p className="text-white/60 text-xs">
                  © 2024 FinoVators • Powered by Advanced AI • Secured by Quantum Encryption
                </p>
              </div>
            </div>
          </div>
        </footer>
      </div>
    </div>
  );
};

export default Index;
