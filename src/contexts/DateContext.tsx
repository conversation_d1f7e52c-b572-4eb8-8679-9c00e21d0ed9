import React, { createContext, useContext, useState, useEffect, ReactNode } from 'react';
import { startOfMonth } from 'date-fns';
import { 
  getDataForMonth, 
  Transaction, 
  PaymentSummaryData, 
  BadgeData, 
  SuggestionData 
} from '../services/mockDataService';

interface DateContextType {
  selectedDate: Date;
  setSelectedDate: (date: Date) => void;
  transactions: Transaction[];
  paymentSummary: PaymentSummaryData;
  badges: BadgeData[];
  suggestions: SuggestionData[];
  isLoading: boolean;
}

const DateContext = createContext<DateContextType | undefined>(undefined);

interface DateProviderProps {
  children: ReactNode;
}

export const DateProvider: React.FC<DateProviderProps> = ({ children }) => {
  const [selectedDate, setSelectedDate] = useState<Date>(startOfMonth(new Date()));
  const [transactions, setTransactions] = useState<Transaction[]>([]);
  const [paymentSummary, setPaymentSummary] = useState<PaymentSummaryData>({
    currentBalance: 0,
    creditLimit: 10000,
    availableCredit: 10000,
    minimumPayment: 0,
    dueDate: '',
    totalSpent: 0,
    cashbackEarned: 0,
    paymentStatus: 'On Time'
  });
  const [badges, setBadges] = useState<BadgeData[]>([]);
  const [suggestions, setSuggestions] = useState<SuggestionData[]>([]);
  const [isLoading, setIsLoading] = useState(false);

  // Load data when selected date changes
  useEffect(() => {
    const loadDataForDate = async () => {
      setIsLoading(true);
      
      // Simulate API call delay
      await new Promise(resolve => setTimeout(resolve, 300));
      
      try {
        const data = getDataForMonth(selectedDate);
        setTransactions(data.transactions);
        setPaymentSummary(data.paymentSummary);
        setBadges(data.badges);
        setSuggestions(data.suggestions);
      } catch (error) {
        console.error('Error loading data for date:', error);
      } finally {
        setIsLoading(false);
      }
    };

    loadDataForDate();
  }, [selectedDate]);

  const handleDateChange = (date: Date) => {
    setSelectedDate(startOfMonth(date));
  };

  const value: DateContextType = {
    selectedDate,
    setSelectedDate: handleDateChange,
    transactions,
    paymentSummary,
    badges,
    suggestions,
    isLoading
  };

  return (
    <DateContext.Provider value={value}>
      {children}
    </DateContext.Provider>
  );
};

export const useDateContext = (): DateContextType => {
  const context = useContext(DateContext);
  if (context === undefined) {
    throw new Error('useDateContext must be used within a DateProvider');
  }
  return context;
};

export default DateContext;
