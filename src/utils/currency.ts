// Utility functions for Indian currency formatting

export const formatIndianCurrency = (amount: number): string => {
  return new Intl.NumberFormat('en-IN', {
    style: 'currency',
    currency: 'INR',
    minimumFractionDigits: 0,
    maximumFractionDigits: 0,
  }).format(amount);
};

export const formatIndianNumber = (amount: number): string => {
  return new Intl.NumberFormat('en-IN').format(amount);
};

// Convert USD amounts to approximate INR (using 1 USD = 83.2 INR)
export const convertUSDToINR = (usdAmount: number): number => {
  return Math.round(usdAmount * 83.2);
};
